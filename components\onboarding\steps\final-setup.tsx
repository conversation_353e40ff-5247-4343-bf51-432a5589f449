"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { CheckCircle, Rocket, Star, Target } from "lucide-react"

interface FinalSetupProps {
  data: any
  onNext: (data: any) => void
  onBack?: () => void
}

export function FinalSetup({ data, onNext, onBack }: FinalSetupProps) {
  const [isCreating, setIsCreating] = useState(false)

  const handleComplete = () => {
    setIsCreating(true)
    // Simulate account creation
    setTimeout(() => {
      onNext({ setupComplete: true })
    }, 3000)
  }

  const getPersonalizedFeatures = () => {
    const features = []

    // Based on priorities
    if (data.topPriorities?.includes("va-benefits")) {
      features.push({
        icon: "🏥",
        title: "VA Claims Tracker",
        description: "Real-time tracking of your disability claim with personalized timeline",
      })
    }

    if (data.topPriorities?.includes("job-search")) {
      features.push({
        icon: "💼",
        title: "Smart Job Matching",
        description: `Jobs in ${data.targetIndustry} industry in ${data.targetLocation}`,
      })
    }

    if (data.topPriorities?.includes("financial-security")) {
      features.push({
        icon: "💰",
        title: "Financial Planning Tools",
        description: `Retirement planning for ${data.salaryRange} target income`,
      })
    }

    // Based on location
    if (data.targetLocation) {
      features.push({
        icon: "📍",
        title: "State Benefits Calculator",
        description: `${data.targetLocation} veteran benefits and cost of living analysis`,
      })
    }

    // Based on support needs
    if (data.supportNeeds?.includes("ai-coaching")) {
      features.push({
        icon: "🤖",
        title: "AI Transition Coach",
        description: "Personalized guidance based on your military background and goals",
      })
    }

    if (data.supportNeeds?.includes("peer-mentorship")) {
      features.push({
        icon: "👥",
        title: "Peer Mentorship Network",
        description: `Connect with ${data.branch} veterans in ${data.targetIndustry}`,
      })
    }

    // Based on family status
    if (data.familyStatus?.includes("married") || data.familyStatus?.includes("parent")) {
      features.push({
        icon: "👨‍👩‍👧‍👦",
        title: "Family Dashboard",
        description: "Resources and planning tools for your family's transition",
      })
    }

    return features.slice(0, 6) // Show top 6 features
  }

  const getTimelinePreview = () => {
    const separationDate = new Date(data.separationDate)
    const today = new Date()
    const daysUntil = Math.ceil((separationDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))

    if (daysUntil > 365) {
      return "12+ months timeline with early preparation focus"
    } else if (daysUntil > 180) {
      return "6-12 months timeline with active preparation"
    } else if (daysUntil > 90) {
      return "3-6 months timeline with final preparations"
    } else {
      return "Final 90 days timeline with urgent tasks"
    }
  }

  if (isCreating) {
    return (
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="pt-12 pb-12 text-center">
          <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
            <Rocket className="w-8 h-8 text-white animate-pulse" />
          </div>
          <h3 className="text-2xl font-bold text-white mb-4">Creating Your Command Center</h3>
          <p className="text-slate-400 mb-6">
            We're setting up your personalized transition dashboard based on your responses...
          </p>
          <Progress value={66} className="w-64 mx-auto mb-4" />
          <div className="space-y-2 text-sm text-slate-400">
            <div className="flex items-center justify-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-400" />
              <span>Analyzing your military background</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <CheckCircle className="w-4 h-4 text-green-400" />
              <span>Customizing your timeline</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
              <span>Setting up your personalized features</span>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Summary Header */}
      <Card className="bg-gradient-to-r from-blue-600 to-purple-600 border-0">
        <CardContent className="pt-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-2">🎉 Your MTCC is Ready!</h2>
            <p className="text-blue-100">
              Based on your responses, we've created a personalized transition command center just for you.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Profile Summary */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Your Profile Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="text-slate-400 text-sm mb-2">Military Background</h4>
              <div className="space-y-1">
                <p className="text-white">
                  {data.rank}, {data.branch}
                </p>
                <p className="text-slate-300">
                  {data.mos} • {data.yearsOfService} years
                </p>
                <p className="text-slate-300">{data.clearanceLevel} clearance</p>
              </div>
            </div>
            <div>
              <h4 className="text-slate-400 text-sm mb-2">Transition Goals</h4>
              <div className="space-y-1">
                <p className="text-white">{data.targetLocation}</p>
                <p className="text-slate-300">{data.targetIndustry}</p>
                <p className="text-slate-300">{data.salaryRange}</p>
              </div>
            </div>
            <div>
              <h4 className="text-slate-400 text-sm mb-2">Timeline</h4>
              <div className="space-y-1">
                <p className="text-white">{getTimelinePreview()}</p>
                <p className="text-slate-300">{data.transitionPhase}</p>
                <p className="text-slate-300">{data.timeCommitment}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Priorities */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Star className="w-5 h-5 mr-2" />
            Your Top Priorities
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            {data.topPriorities?.map((priorityId: string) => {
              const priorityLabels: { [key: string]: { label: string; icon: string } } = {
                "va-benefits": { label: "Maximize VA Benefits", icon: "🏥" },
                "job-search": { label: "Land a Great Job", icon: "💼" },
                "financial-security": { label: "Financial Security", icon: "💰" },
                education: { label: "Education & Training", icon: "🎓" },
                "family-transition": { label: "Family Transition", icon: "👨‍👩‍👧‍👦" },
                "location-move": { label: "Relocation", icon: "🏠" },
              }
              const priority = priorityLabels[priorityId]
              return priority ? (
                <Badge key={priorityId} variant="outline" className="border-blue-600 text-blue-400 px-3 py-1">
                  {priority.icon} {priority.label}
                </Badge>
              ) : null
            })}
          </div>
        </CardContent>
      </Card>

      {/* Personalized Features */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Target className="w-5 h-5 mr-2" />
            Your Personalized Features
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {getPersonalizedFeatures().map((feature, index) => (
              <div key={index} className="border border-slate-700 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <span className="text-2xl">{feature.icon}</span>
                  <div>
                    <h4 className="text-white font-medium mb-1">{feature.title}</h4>
                    <p className="text-slate-400 text-sm">{feature.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* What Happens Next */}
      <Card className="bg-green-900/20 border-green-700/30">
        <CardHeader>
          <CardTitle className="text-green-300 flex items-center">
            <Rocket className="w-5 h-5 mr-2" />
            What Happens Next
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                1
              </div>
              <div>
                <p className="text-green-200 font-medium">Instant Dashboard Setup</p>
                <p className="text-green-300 text-sm">Your personalized command center will be ready immediately</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                2
              </div>
              <div>
                <p className="text-green-200 font-medium">Smart Task Prioritization</p>
                <p className="text-green-300 text-sm">We'll show you exactly what to do next based on your timeline</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                3
              </div>
              <div>
                <p className="text-green-200 font-medium">Continuous Personalization</p>
                <p className="text-green-300 text-sm">Your experience gets smarter as you use the platform</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between pt-6">
        {onBack && (
          <Button type="button" variant="outline" onClick={onBack} className="border-slate-600 text-slate-300">
            Back
          </Button>
        )}
        <Button onClick={handleComplete} className="bg-green-600 hover:bg-green-700 ml-auto px-8">
          <Rocket className="w-4 h-4 mr-2" />
          Launch My Command Center
        </Button>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Calculator, DollarSign, TrendingUp, PiggyBank, Home, GraduationCap } from "lucide-react"

export function FinancialCalculator() {
  const [retirementPay, setRetirementPay] = useState("4500")
  const [vaRating, setVaRating] = useState("50")
  const [yearsOfService, setYearsOfService] = useState("20")
  const [currentAge, setCurrentAge] = useState("42")
  const [targetRetirementAge, setTargetRetirementAge] = useState("65")
  const [currentSavings, setCurrentSavings] = useState("150000")
  const [monthlyContribution, set<PERSON><PERSON>hlyContribution] = useState("500")

  // Calculate VA compensation based on rating
  const getVACompensation = (rating: number) => {
    const rates: { [key: number]: number } = {
      10: 171.23,
      20: 338.49,
      30: 524.31,
      40: 755.28,
      50: 1075.16,
      60: 1361.88,
      70: 1716.28,
      80: 1995.01,
      90: 2241.91,
      100: 3737.85,
    }
    return rates[rating] || 0
  }

  const vaCompensation = getVACompensation(Number.parseInt(vaRating))
  const totalMonthlyIncome = Number.parseInt(retirementPay) + vaCompensation
  const annualIncome = totalMonthlyIncome * 12

  // Calculate retirement projections
  const yearsToRetirement = Number.parseInt(targetRetirementAge) - Number.parseInt(currentAge)
  const monthsToRetirement = yearsToRetirement * 12
  const annualReturn = 0.07 // 7% average return
  const monthlyReturn = annualReturn / 12

  // Future value calculation
  const futureValue =
    Number.parseInt(currentSavings) * Math.pow(1 + annualReturn, yearsToRetirement) +
    (Number.parseInt(monthlyContribution) * (Math.pow(1 + monthlyReturn, monthsToRetirement) - 1)) / monthlyReturn

  const scenarios = [
    {
      title: "Conservative (4% withdrawal)",
      monthlyIncome: (futureValue * 0.04) / 12,
      description: "Safe withdrawal rate for long-term sustainability",
    },
    {
      title: "Moderate (5% withdrawal)",
      monthlyIncome: (futureValue * 0.05) / 12,
      description: "Balanced approach with moderate risk",
    },
    {
      title: "Aggressive (6% withdrawal)",
      monthlyIncome: (futureValue * 0.06) / 12,
      description: "Higher income but increased portfolio risk",
    },
  ]

  const expenses = [
    { category: "Housing", amount: 2500, percentage: 35 },
    { category: "Healthcare", amount: 800, percentage: 11 },
    { category: "Food", amount: 600, percentage: 8 },
    { category: "Transportation", amount: 500, percentage: 7 },
    { category: "Utilities", amount: 300, percentage: 4 },
    { category: "Insurance", amount: 400, percentage: 6 },
    { category: "Entertainment", amount: 400, percentage: 6 },
    { category: "Miscellaneous", amount: 500, percentage: 7 },
    { category: "Savings", amount: 1000, percentage: 14 },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">Financial Planning Calculator</h1>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <PiggyBank className="w-4 h-4 mr-2" />
          Save Plan
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Input Form */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Calculator className="w-5 h-5 mr-2" />
              Your Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="retirement-pay" className="text-slate-300">
                Monthly Retirement Pay
              </Label>
              <Input
                id="retirement-pay"
                value={retirementPay}
                onChange={(e) => setRetirementPay(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="4500"
              />
            </div>

            <div>
              <Label htmlFor="va-rating" className="text-slate-300">
                VA Disability Rating (%)
              </Label>
              <Select value={vaRating} onValueChange={setVaRating}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="Select rating" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  <SelectItem value="0">0%</SelectItem>
                  <SelectItem value="10">10%</SelectItem>
                  <SelectItem value="20">20%</SelectItem>
                  <SelectItem value="30">30%</SelectItem>
                  <SelectItem value="40">40%</SelectItem>
                  <SelectItem value="50">50%</SelectItem>
                  <SelectItem value="60">60%</SelectItem>
                  <SelectItem value="70">70%</SelectItem>
                  <SelectItem value="80">80%</SelectItem>
                  <SelectItem value="90">90%</SelectItem>
                  <SelectItem value="100">100%</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="years-service" className="text-slate-300">
                Years of Service
              </Label>
              <Input
                id="years-service"
                value={yearsOfService}
                onChange={(e) => setYearsOfService(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="20"
              />
            </div>

            <div>
              <Label htmlFor="current-age" className="text-slate-300">
                Current Age
              </Label>
              <Input
                id="current-age"
                value={currentAge}
                onChange={(e) => setCurrentAge(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="42"
              />
            </div>

            <div>
              <Label htmlFor="target-age" className="text-slate-300">
                Target Retirement Age
              </Label>
              <Input
                id="target-age"
                value={targetRetirementAge}
                onChange={(e) => setTargetRetirementAge(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="65"
              />
            </div>

            <div>
              <Label htmlFor="current-savings" className="text-slate-300">
                Current Savings/TSP
              </Label>
              <Input
                id="current-savings"
                value={currentSavings}
                onChange={(e) => setCurrentSavings(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="150000"
              />
            </div>

            <div>
              <Label htmlFor="monthly-contribution" className="text-slate-300">
                Monthly Contribution
              </Label>
              <Input
                id="monthly-contribution"
                value={monthlyContribution}
                onChange={(e) => setMonthlyContribution(e.target.value)}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="500"
              />
            </div>
          </CardContent>
        </Card>

        {/* Current Income Analysis */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <DollarSign className="w-5 h-5 mr-2" />
              Current Income Analysis
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-4">
              <h4 className="text-blue-300 font-medium mb-2">Monthly Income Breakdown</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-slate-300">Military Retirement:</span>
                  <span className="text-white">${Number.parseInt(retirementPay).toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-300">VA Compensation:</span>
                  <span className="text-white">${vaCompensation.toLocaleString()}</span>
                </div>
                <div className="border-t border-blue-700/30 pt-2">
                  <div className="flex justify-between font-semibold">
                    <span className="text-blue-300">Total Monthly:</span>
                    <span className="text-blue-300">${totalMonthlyIncome.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-green-900/20 border border-green-700/30 rounded-lg p-4">
              <h4 className="text-green-300 font-medium mb-2">Annual Income</h4>
              <p className="text-2xl font-bold text-green-400">${annualIncome.toLocaleString()}</p>
              <p className="text-green-200 text-sm">Tax advantages: VA compensation is tax-free</p>
            </div>

            <div className="space-y-3">
              <h4 className="text-white font-medium">Income Replacement Ratio</h4>
              <div className="bg-slate-700 rounded-lg p-3">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-300">vs. Active Duty Pay</span>
                  <span className="text-white">~75%</span>
                </div>
                <div className="w-full bg-slate-600 rounded-full h-2 mt-2">
                  <div className="bg-blue-400 h-2 rounded-full" style={{ width: "75%" }}></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Retirement Projections */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Retirement Projections
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-purple-900/20 border border-purple-700/30 rounded-lg p-4">
              <h4 className="text-purple-300 font-medium mb-2">Portfolio at Age {targetRetirementAge}</h4>
              <p className="text-2xl font-bold text-purple-400">${futureValue.toLocaleString()}</p>
              <p className="text-purple-200 text-sm">Based on 7% annual return</p>
            </div>

            <div className="space-y-3">
              <h4 className="text-white font-medium">Withdrawal Scenarios</h4>
              {scenarios.map((scenario, index) => (
                <div key={index} className="bg-slate-700 rounded-lg p-3">
                  <div className="flex justify-between items-start mb-1">
                    <span className="text-slate-300 text-sm font-medium">{scenario.title}</span>
                    <span className="text-white font-semibold">${scenario.monthlyIncome.toLocaleString()}/mo</span>
                  </div>
                  <p className="text-slate-400 text-xs">{scenario.description}</p>
                </div>
              ))}
            </div>

            <div className="bg-yellow-900/20 border border-yellow-700/30 rounded-lg p-3">
              <p className="text-yellow-300 text-sm">
                <strong>Note:</strong> These projections don't include Social Security, which you'll be eligible for at
                62.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Budget Breakdown */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Recommended Budget Breakdown</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="md:col-span-2">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {expenses.map((expense, index) => (
                  <div key={index} className="bg-slate-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-white font-medium text-sm">{expense.category}</h4>
                      <span className="text-slate-400 text-xs">{expense.percentage}%</span>
                    </div>
                    <p className="text-xl font-bold text-blue-400">${expense.amount}</p>
                    <div className="w-full bg-slate-600 rounded-full h-1 mt-2">
                      <div
                        className="bg-blue-400 h-1 rounded-full"
                        style={{ width: `${expense.percentage * 2}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-4">
                <h4 className="text-blue-300 font-medium mb-2 flex items-center">
                  <Home className="w-4 h-4 mr-2" />
                  Housing Considerations
                </h4>
                <ul className="text-blue-200 text-sm space-y-1">
                  <li>• Consider VA home loan benefits</li>
                  <li>• Property tax exemptions available</li>
                  <li>• Factor in state-specific benefits</li>
                </ul>
              </div>

              <div className="bg-green-900/20 border border-green-700/30 rounded-lg p-4">
                <h4 className="text-green-300 font-medium mb-2 flex items-center">
                  <GraduationCap className="w-4 h-4 mr-2" />
                  Education Benefits
                </h4>
                <ul className="text-green-200 text-sm space-y-1">
                  <li>• GI Bill transferability</li>
                  <li>• State education benefits</li>
                  <li>• Professional certifications</li>
                </ul>
              </div>

              <div className="bg-yellow-900/20 border border-yellow-700/30 rounded-lg p-4">
                <h4 className="text-yellow-300 font-medium mb-2">Emergency Fund</h4>
                <p className="text-yellow-200 text-sm">Recommended: 6-12 months of expenses</p>
                <p className="text-xl font-bold text-yellow-400 mt-1">$42,000 - $84,000</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

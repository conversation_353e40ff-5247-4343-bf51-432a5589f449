"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Command,
  Shield,
  AlertTriangle,
  TrendingUp,
  MapPin,
  Clock,
  Target,
  Zap,
  Brain,
  Users,
  FileText,
  Calendar,
  Activity,
  Radar,
  Satellite,
  Radio,
  Eye,
} from "lucide-react"

export function MissionControl() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [missionStatus, setMissionStatus] = useState("ACTIVE")

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  // Mission Critical Status
  const missionMetrics = {
    overallReadiness: 73,
    threatLevel: "MODERATE",
    resourceUtilization: 68,
    timelineAdherence: 85,
    successProbability: 89,
  }

  // Real-time Intelligence Feed
  const intelligenceFeed = [
    {
      id: 1,
      type: "OPPORTUNITY",
      priority: "HIGH",
      message: "3 new job matches found in target location",
      timestamp: "2 min ago",
      source: "Job Intelligence",
      actionable: true,
    },
    {
      id: 2,
      type: "ALERT",
      priority: "CRITICAL",
      message: "VA claim deadline approaching in 5 days",
      timestamp: "15 min ago",
      source: "Benefits Monitor",
      actionable: true,
    },
    {
      id: 3,
      type: "INTEL",
      priority: "MEDIUM",
      message: "Target company hiring surge detected (+40%)",
      timestamp: "1 hour ago",
      source: "Market Analysis",
      actionable: false,
    },
    {
      id: 4,
      type: "UPDATE",
      priority: "LOW",
      message: "State benefits calculator updated with new rates",
      timestamp: "3 hours ago",
      source: "System Update",
      actionable: false,
    },
  ]

  // Threat Assessment Matrix
  const threats = [
    {
      category: "Timeline Risk",
      level: "HIGH",
      impact: 8,
      probability: 6,
      description: "Multiple deadlines converging in next 30 days",
      mitigation: "Prioritize critical path items",
      status: "MONITORING",
    },
    {
      category: "Financial Risk",
      level: "MEDIUM",
      impact: 7,
      probability: 4,
      description: "Gap in income during transition period",
      mitigation: "Emergency fund activated",
      status: "MITIGATED",
    },
    {
      category: "Market Risk",
      level: "LOW",
      impact: 5,
      probability: 3,
      description: "Economic downturn affecting job market",
      mitigation: "Diversified job search strategy",
      status: "MONITORED",
    },
  ]

  // Resource Allocation
  const resources = [
    { name: "Time", allocated: 75, available: 25, unit: "hours/week", status: "OPTIMAL" },
    { name: "Budget", allocated: 60, available: 40, unit: "$", status: "GOOD" },
    { name: "Energy", allocated: 80, available: 20, unit: "capacity", status: "HIGH" },
    { name: "Support", allocated: 45, available: 55, unit: "network", status: "UNDERUTILIZED" },
  ]

  // Situational Awareness
  const situationalData = {
    location: {
      current: "San Diego, CA",
      target: "Austin, TX",
      marketConditions: "FAVORABLE",
      costOfLiving: "+15%",
    },
    timeline: {
      phase: "ACTIVE TRANSITION",
      daysRemaining: 180,
      criticalPath: "VA Claims → Job Search → Relocation",
      nextMilestone: "TAP Workshop Completion",
    },
    network: {
      activeConnections: 47,
      newContacts: 8,
      mentorSessions: 3,
      referrals: 2,
    },
  }

  const getThreatColor = (level: string) => {
    switch (level) {
      case "HIGH":
        return "text-red-400 bg-red-900/20 border-red-700/30"
      case "MEDIUM":
        return "text-yellow-400 bg-yellow-900/20 border-yellow-700/30"
      case "LOW":
        return "text-green-400 bg-green-900/20 border-green-700/30"
      default:
        return "text-slate-400 bg-slate-900/20 border-slate-700/30"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "OPTIMAL":
      case "GOOD":
        return "text-green-400"
      case "HIGH":
      case "MONITORING":
        return "text-yellow-400"
      case "CRITICAL":
        return "text-red-400"
      default:
        return "text-slate-400"
    }
  }

  return (
    <div className="space-y-6">
      {/* Command Header */}
      <div className="bg-gradient-to-r from-slate-800 to-slate-900 border border-slate-700 rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
              <Command className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">Mission Control Center</h1>
              <p className="text-slate-400">Transition Command & Control</p>
            </div>
          </div>
          <div className="text-right">
            <div className="flex items-center space-x-4">
              <div>
                <div className="text-white font-mono text-lg">{currentTime.toLocaleTimeString()}</div>
                <div className="text-slate-400 text-sm">{currentTime.toLocaleDateString()}</div>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                <Badge variant="outline" className="border-green-600 text-green-400">
                  {missionStatus}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mission Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Readiness</p>
                <p className="text-2xl font-bold text-white">{missionMetrics.overallReadiness}%</p>
              </div>
              <Shield className="w-8 h-8 text-blue-400" />
            </div>
            <Progress value={missionMetrics.overallReadiness} className="mt-2 h-2" />
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Threat Level</p>
                <p className="text-lg font-bold text-yellow-400">{missionMetrics.threatLevel}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Resources</p>
                <p className="text-2xl font-bold text-white">{missionMetrics.resourceUtilization}%</p>
              </div>
              <Target className="w-8 h-8 text-purple-400" />
            </div>
            <Progress value={missionMetrics.resourceUtilization} className="mt-2 h-2" />
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Timeline</p>
                <p className="text-2xl font-bold text-white">{missionMetrics.timelineAdherence}%</p>
              </div>
              <Clock className="w-8 h-8 text-green-400" />
            </div>
            <Progress value={missionMetrics.timelineAdherence} className="mt-2 h-2" />
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Success Prob.</p>
                <p className="text-2xl font-bold text-green-400">{missionMetrics.successProbability}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-400" />
            </div>
            <Progress value={missionMetrics.successProbability} className="mt-2 h-2" />
          </CardContent>
        </Card>
      </div>

      {/* Main Command Interface */}
      <Tabs defaultValue="intelligence" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 bg-slate-800 border-slate-700">
          <TabsTrigger value="intelligence" className="flex items-center space-x-2">
            <Brain className="w-4 h-4" />
            <span>Intel</span>
          </TabsTrigger>
          <TabsTrigger value="threats" className="flex items-center space-x-2">
            <AlertTriangle className="w-4 h-4" />
            <span>Threats</span>
          </TabsTrigger>
          <TabsTrigger value="resources" className="flex items-center space-x-2">
            <Target className="w-4 h-4" />
            <span>Resources</span>
          </TabsTrigger>
          <TabsTrigger value="situational" className="flex items-center space-x-2">
            <Radar className="w-4 h-4" />
            <span>Situation</span>
          </TabsTrigger>
          <TabsTrigger value="operations" className="flex items-center space-x-2">
            <Activity className="w-4 h-4" />
            <span>Ops</span>
          </TabsTrigger>
        </TabsList>

        {/* Intelligence Feed */}
        <TabsContent value="intelligence">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Radio className="w-5 h-5 mr-2" />
                    Live Intelligence Feed
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {intelligenceFeed.map((item) => (
                    <div
                      key={item.id}
                      className={`border rounded-lg p-4 ${
                        item.priority === "CRITICAL"
                          ? "border-red-700/30 bg-red-900/10"
                          : item.priority === "HIGH"
                            ? "border-yellow-700/30 bg-yellow-900/10"
                            : "border-slate-700 bg-slate-800/50"
                      }`}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Badge variant={item.priority === "CRITICAL" ? "destructive" : "outline"} className="text-xs">
                            {item.type}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {item.priority}
                          </Badge>
                        </div>
                        <span className="text-slate-400 text-xs">{item.timestamp}</span>
                      </div>
                      <p className="text-white text-sm mb-2">{item.message}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-slate-400 text-xs">Source: {item.source}</span>
                        {item.actionable && (
                          <Button size="sm" variant="outline" className="border-slate-600 text-slate-300">
                            Take Action
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            <div className="space-y-6">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Satellite className="w-5 h-5 mr-2" />
                    Market Intelligence
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-slate-400">Job Market</span>
                    <span className="text-green-400">STRONG</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Housing Market</span>
                    <span className="text-yellow-400">COMPETITIVE</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-400">Economic Outlook</span>
                    <span className="text-green-400">POSITIVE</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Eye className="w-5 h-5 mr-2" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700" size="sm">
                    <Zap className="w-4 h-4 mr-2" />
                    Execute Priority Tasks
                  </Button>
                  <Button variant="outline" className="w-full border-slate-600 text-slate-300" size="sm">
                    <Users className="w-4 h-4 mr-2" />
                    Contact Support Team
                  </Button>
                  <Button variant="outline" className="w-full border-slate-600 text-slate-300" size="sm">
                    <FileText className="w-4 h-4 mr-2" />
                    Generate Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Threat Assessment */}
        <TabsContent value="threats">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Threat Assessment Matrix</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {threats.map((threat, index) => (
                  <div key={index} className={`border rounded-lg p-4 ${getThreatColor(threat.level)}`}>
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold">{threat.category}</h4>
                      <Badge variant="outline" className="text-xs">
                        {threat.level}
                      </Badge>
                    </div>
                    <p className="text-sm mb-3 opacity-90">{threat.description}</p>
                    <div className="space-y-2 text-xs">
                      <div className="flex justify-between">
                        <span>Impact:</span>
                        <span>{threat.impact}/10</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Probability:</span>
                        <span>{threat.probability}/10</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Status:</span>
                        <span>{threat.status}</span>
                      </div>
                    </div>
                    <div className="mt-3 pt-3 border-t border-current/20">
                      <p className="text-xs font-medium">Mitigation:</p>
                      <p className="text-xs opacity-75">{threat.mitigation}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Resource Management */}
        <TabsContent value="resources">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Resource Allocation & Utilization</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {resources.map((resource, index) => (
                  <div key={index} className="border border-slate-700 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="text-white font-medium">{resource.name}</h4>
                      <Badge variant="outline" className={getStatusColor(resource.status)}>
                        {resource.status}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-slate-400">Allocated</span>
                        <span className="text-white">{resource.allocated}%</span>
                      </div>
                      <Progress value={resource.allocated} className="h-2" />
                      <div className="flex justify-between text-xs text-slate-400">
                        <span>Available: {resource.available}%</span>
                        <span>{resource.unit}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Situational Awareness */}
        <TabsContent value="situational">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <MapPin className="w-5 h-5 mr-2" />
                  Geographic Situation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-slate-400 text-sm">Current Location</p>
                    <p className="text-white font-medium">{situationalData.location.current}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Target Location</p>
                    <p className="text-white font-medium">{situationalData.location.target}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Market Conditions</p>
                    <p className="text-green-400 font-medium">{situationalData.location.marketConditions}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Cost of Living</p>
                    <p className="text-yellow-400 font-medium">{situationalData.location.costOfLiving}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Mission Timeline
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <p className="text-slate-400 text-sm">Current Phase</p>
                    <p className="text-white font-medium">{situationalData.timeline.phase}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Days Remaining</p>
                    <p className="text-white font-medium">{situationalData.timeline.daysRemaining}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Critical Path</p>
                    <p className="text-white font-medium">{situationalData.timeline.criticalPath}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Next Milestone</p>
                    <p className="text-blue-400 font-medium">{situationalData.timeline.nextMilestone}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Operations */}
        <TabsContent value="operations">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Active Operations</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border border-slate-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-white font-medium">Operation: Job Search</h4>
                    <Badge className="bg-green-600">ACTIVE</Badge>
                  </div>
                  <p className="text-slate-400 text-sm mb-2">Systematic job search and application process</p>
                  <Progress value={65} className="mb-2" />
                  <div className="flex justify-between text-xs text-slate-400">
                    <span>Progress: 65%</span>
                    <span>ETA: 45 days</span>
                  </div>
                </div>

                <div className="border border-slate-700 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-white font-medium">Operation: VA Claims</h4>
                    <Badge className="bg-yellow-600">PENDING</Badge>
                  </div>
                  <p className="text-slate-400 text-sm mb-2">Disability compensation claim processing</p>
                  <Progress value={80} className="mb-2" />
                  <div className="flex justify-between text-xs text-slate-400">
                    <span>Progress: 80%</span>
                    <span>ETA: 30 days</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Command Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full bg-red-600 hover:bg-red-700">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  Execute Emergency Protocol
                </Button>
                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  <Zap className="w-4 h-4 mr-2" />
                  Accelerate Timeline
                </Button>
                <Button className="w-full bg-green-600 hover:bg-green-700">
                  <Target className="w-4 h-4 mr-2" />
                  Optimize Resources
                </Button>
                <Button variant="outline" className="w-full border-slate-600 text-slate-300">
                  <FileText className="w-4 h-4 mr-2" />
                  Generate Sitrep
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

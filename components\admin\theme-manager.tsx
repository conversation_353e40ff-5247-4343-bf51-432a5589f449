"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Palette, Save, Download, Upload, RefreshCw } from "lucide-react"

export function ThemeManager() {
  const [selectedTheme, setSelectedTheme] = useState("default")
  const [customColors, setCustomColors] = useState({
    primary: "#3b82f6",
    secondary: "#64748b",
    accent: "#8b5cf6",
    background: "#0f172a",
    surface: "#1e293b",
    text: "#f8fafc",
    textSecondary: "#94a3b8",
  })

  const predefinedThemes = [
    {
      id: "default",
      name: "Default Dark",
      description: "Standard MTCC dark theme",
      colors: {
        primary: "#3b82f6",
        secondary: "#64748b",
        accent: "#8b5cf6",
        background: "#0f172a",
        surface: "#1e293b",
        text: "#f8fafc",
        textSecondary: "#94a3b8",
      },
    },
    {
      id: "military-green",
      name: "Military Green",
      description: "Traditional military color scheme",
      colors: {
        primary: "#16a34a",
        secondary: "#65a30d",
        accent: "#eab308",
        background: "#0c1f0c",
        surface: "#1a2e1a",
        text: "#f0fdf4",
        textSecondary: "#bbf7d0",
      },
    },
    {
      id: "navy-blue",
      name: "Navy Blue",
      description: "Navy-inspired color palette",
      colors: {
        primary: "#1e40af",
        secondary: "#3730a3",
        accent: "#0ea5e9",
        background: "#0c1426",
        surface: "#1e2a47",
        text: "#f1f5f9",
        textSecondary: "#cbd5e1",
      },
    },
    {
      id: "high-contrast",
      name: "High Contrast",
      description: "Accessibility-focused high contrast theme",
      colors: {
        primary: "#ffffff",
        secondary: "#000000",
        accent: "#ffff00",
        background: "#000000",
        surface: "#1a1a1a",
        text: "#ffffff",
        textSecondary: "#cccccc",
      },
    },
  ]

  const componentSettings = [
    {
      category: "Navigation",
      components: [
        { name: "Sidebar Background", setting: "sidebar-bg", value: "#1e293b" },
        { name: "Sidebar Text", setting: "sidebar-text", value: "#f8fafc" },
        { name: "Active Item", setting: "sidebar-active", value: "#3b82f6" },
        { name: "Hover State", setting: "sidebar-hover", value: "#334155" },
      ],
    },
    {
      category: "Cards & Panels",
      components: [
        { name: "Card Background", setting: "card-bg", value: "#1e293b" },
        { name: "Card Border", setting: "card-border", value: "#334155" },
        { name: "Header Background", setting: "header-bg", value: "#0f172a" },
        { name: "Panel Shadow", setting: "panel-shadow", value: "rgba(0,0,0,0.5)" },
      ],
    },
    {
      category: "Interactive Elements",
      components: [
        { name: "Button Primary", setting: "btn-primary", value: "#3b82f6" },
        { name: "Button Secondary", setting: "btn-secondary", value: "#64748b" },
        { name: "Input Background", setting: "input-bg", value: "#334155" },
        { name: "Input Border", setting: "input-border", value: "#475569" },
      ],
    },
    {
      category: "Status & Alerts",
      components: [
        { name: "Success Color", setting: "success", value: "#10b981" },
        { name: "Warning Color", setting: "warning", value: "#f59e0b" },
        { name: "Error Color", setting: "error", value: "#ef4444" },
        { name: "Info Color", setting: "info", value: "#3b82f6" },
      ],
    },
  ]

  const handleThemeSelect = (themeId: string) => {
    const theme = predefinedThemes.find((t) => t.id === themeId)
    if (theme) {
      setSelectedTheme(themeId)
      setCustomColors(theme.colors)
    }
  }

  const handleColorChange = (colorKey: string, value: string) => {
    setCustomColors((prev) => ({
      ...prev,
      [colorKey]: value,
    }))
  }

  const handleSaveTheme = () => {
    // In production, this would save to your backend
    console.log("Saving theme:", { selectedTheme, customColors })
  }

  const handleExportTheme = () => {
    const themeData = {
      name: selectedTheme,
      colors: customColors,
      components: componentSettings,
      exportDate: new Date().toISOString(),
    }

    const blob = new Blob([JSON.stringify(themeData, null, 2)], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `mtcc-theme-${selectedTheme}.json`
    a.click()
  }

  return (
    <div className="space-y-6">
      {/* Theme Selection */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Palette className="w-5 h-5 mr-2" />
            Theme Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {predefinedThemes.map((theme) => (
              <div
                key={theme.id}
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedTheme === theme.id
                    ? "border-blue-500 bg-blue-900/20"
                    : "border-slate-700 hover:border-slate-600"
                }`}
                onClick={() => handleThemeSelect(theme.id)}
              >
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-white font-medium">{theme.name}</h4>
                  {selectedTheme === theme.id && <Badge className="bg-blue-600">Active</Badge>}
                </div>
                <p className="text-slate-400 text-sm mb-3">{theme.description}</p>
                <div className="flex space-x-1">
                  {Object.values(theme.colors)
                    .slice(0, 5)
                    .map((color, index) => (
                      <div
                        key={index}
                        className="w-6 h-6 rounded border border-slate-600"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Theme Customization */}
      <Tabs defaultValue="colors" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-slate-800 border-slate-700">
          <TabsTrigger value="colors">Colors</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="typography">Typography</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="colors">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Color Customization</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Object.entries(customColors).map(([key, value]) => (
                  <div key={key} className="space-y-2">
                    <Label className="text-slate-300 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</Label>
                    <div className="flex space-x-2">
                      <Input
                        type="color"
                        value={value}
                        onChange={(e) => handleColorChange(key, e.target.value)}
                        className="w-16 h-10 p-1 bg-slate-700 border-slate-600"
                      />
                      <Input
                        type="text"
                        value={value}
                        onChange={(e) => handleColorChange(key, e.target.value)}
                        className="flex-1 bg-slate-700 border-slate-600 text-white"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="components">
          <div className="space-y-6">
            {componentSettings.map((category) => (
              <Card key={category.category} className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">{category.category}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {category.components.map((component) => (
                      <div
                        key={component.setting}
                        className="flex items-center justify-between p-3 border border-slate-700 rounded-lg"
                      >
                        <div>
                          <p className="text-white text-sm">{component.name}</p>
                          <p className="text-slate-400 text-xs">{component.setting}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-8 h-8 rounded border border-slate-600"
                            style={{ backgroundColor: component.value }}
                          />
                          <Input
                            type="text"
                            value={component.value}
                            className="w-24 h-8 text-xs bg-slate-700 border-slate-600 text-white"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="typography">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Typography Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label className="text-slate-300">Primary Font Family</Label>
                  <Select defaultValue="inter">
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-800 border-slate-700">
                      <SelectItem value="inter">Inter</SelectItem>
                      <SelectItem value="roboto">Roboto</SelectItem>
                      <SelectItem value="system">System UI</SelectItem>
                      <SelectItem value="arial">Arial</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-slate-300">Base Font Size</Label>
                  <Select defaultValue="16">
                    <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-slate-800 border-slate-700">
                      <SelectItem value="14">14px</SelectItem>
                      <SelectItem value="16">16px</SelectItem>
                      <SelectItem value="18">18px</SelectItem>
                      <SelectItem value="20">20px</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="preview">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Theme Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className="border border-slate-700 rounded-lg p-6"
                style={{ backgroundColor: customColors.background }}
              >
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 style={{ color: customColors.text }}>Sample Dashboard</h3>
                    <button
                      className="px-4 py-2 rounded"
                      style={{ backgroundColor: customColors.primary, color: customColors.text }}
                    >
                      Primary Button
                    </button>
                  </div>
                  <div
                    className="p-4 rounded border"
                    style={{
                      backgroundColor: customColors.surface,
                      borderColor: customColors.secondary,
                      color: customColors.text,
                    }}
                  >
                    <h4>Sample Card</h4>
                    <p style={{ color: customColors.textSecondary }}>
                      This is how your theme will look in the application.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <Button onClick={handleExportTheme} variant="outline" className="border-slate-600 text-slate-300">
            <Download className="w-4 h-4 mr-2" />
            Export Theme
          </Button>
          <Button variant="outline" className="border-slate-600 text-slate-300">
            <Upload className="w-4 h-4 mr-2" />
            Import Theme
          </Button>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" className="border-slate-600 text-slate-300">
            <RefreshCw className="w-4 h-4 mr-2" />
            Reset to Default
          </Button>
          <Button onClick={handleSaveTheme} className="bg-green-600 hover:bg-green-700">
            <Save className="w-4 h-4 mr-2" />
            Save Theme
          </Button>
        </div>
      </div>
    </div>
  )
}

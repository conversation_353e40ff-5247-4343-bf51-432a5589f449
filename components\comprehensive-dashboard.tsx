"use client"
import { Box, Typography } from "@mui/material"
import { useAuth } from "../contexts/AuthContext"
import TransitionTimeline from "./TransitionTimeline"

const ComprehensiveDashboard = () => {
  const { user } = useAuth()

  return (
    <Box sx={{ padding: 3 }}>
      <Typography variant="h4" gutterBottom>
        Comprehensive Dashboard
      </Typography>
      <Typography variant="body1">
        Welcome to your personalized dashboard. Here you can track your progress and manage your account.
      </Typography>

      <Box mt={4}>
        <Typography variant="h6" gutterBottom>
          Timeline
        </Typography>
        {user ? <TransitionTimeline user={user} /> : <Typography>Please log in to view your timeline.</Typography>}
      </Box>

      {/* Add more sections as needed */}
    </Box>
  )
}

export default ComprehensiveDashboard

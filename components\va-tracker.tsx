"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { TrendingUp, FileText, Calendar, AlertTriangle, CheckCircle, Clock, Phone } from "lucide-react"

export function VATracker() {
  const claimStatus = {
    submissionDate: "Feb 22, 2023",
    currentPhase: "Evidence Gathering, Review, and Decision",
    estimatedCompletion: "Aug 15, 2024",
    daysInProcess: 145,
    overallProgress: 75,
  }

  const claimItems = [
    {
      condition: "Hearing Loss",
      status: "Evidence Gathering",
      examDate: "Jul 25, 2024",
      examType: "Audiologist",
      rating: "Pending",
      priority: "high",
    },
    {
      condition: "Lower Back Pain",
      status: "Completed",
      examDate: "Jul 10, 2024",
      examType: "General Medical",
      rating: "30%",
      priority: "medium",
    },
    {
      condition: "Sleep Apnea",
      status: "Evidence Gathering",
      examDate: "Jul 28, 2024",
      examType: "Sleep Study",
      rating: "Pending",
      priority: "high",
    },
    {
      condition: "Anxiety",
      status: "Exam Scheduled",
      examDate: "Aug 2, 2024",
      examType: "Mental Health",
      rating: "Pending",
      priority: "medium",
    },
    {
      condition: "Knee Injury",
      status: "Completed",
      examDate: "Jul 5, 2024",
      examType: "Orthopedic",
      rating: "20%",
      priority: "low",
    },
  ]

  const timeline = [
    {
      date: "Feb 22, 2023",
      status: "Claim Received",
      description: "VA acknowledges receipt of your claim",
      completed: true,
    },
    {
      date: "Mar 8, 2023",
      status: "Initial Review",
      description: "Reviewing claim for completeness",
      completed: true,
    },
    {
      date: "Mar 15, 2023",
      status: "Evidence Gathering Started",
      description: "Collecting medical evidence and scheduling exams",
      completed: true,
    },
    {
      date: "Jul 25, 2024",
      status: "C&P Exams in Progress",
      description: "Compensation and Pension examinations being conducted",
      completed: false,
      current: true,
    },
    {
      date: "Aug 15, 2024",
      status: "Decision Phase",
      description: "Final review and rating decision",
      completed: false,
    },
    {
      date: "Aug 30, 2024",
      status: "Claim Complete",
      description: "Decision letter mailed with rating details",
      completed: false,
    },
  ]

  const documents = [
    {
      name: "Service Medical Records",
      status: "Received",
      date: "Mar 1, 2023",
      type: "Medical",
    },
    {
      name: "DD-214",
      status: "Received",
      date: "Feb 22, 2023",
      type: "Military",
    },
    {
      name: "Private Medical Records",
      status: "Pending",
      date: "Requested Jul 20, 2024",
      type: "Medical",
    },
    {
      name: "Buddy Statements",
      status: "Received",
      date: "Jun 15, 2024",
      type: "Supporting",
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "text-green-400"
      case "evidence gathering":
      case "exam scheduled":
        return "text-yellow-400"
      case "pending":
        return "text-blue-400"
      default:
        return "text-slate-400"
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">VA Claim Tracker</h1>
        <div className="flex space-x-2">
          <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
            <Phone className="w-4 h-4 mr-2" />
            Contact VA
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <FileText className="w-4 h-4 mr-2" />
            Upload Document
          </Button>
        </div>
      </div>

      {/* Claim Overview */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Claim Status Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div>
              <p className="text-slate-400 text-sm">Submission Date</p>
              <p className="text-white font-semibold">{claimStatus.submissionDate}</p>
            </div>
            <div>
              <p className="text-slate-400 text-sm">Current Phase</p>
              <p className="text-white font-semibold">{claimStatus.currentPhase}</p>
            </div>
            <div>
              <p className="text-slate-400 text-sm">Days in Process</p>
              <p className="text-white font-semibold">{claimStatus.daysInProcess} days</p>
            </div>
            <div>
              <p className="text-slate-400 text-sm">Est. Completion</p>
              <p className="text-white font-semibold">{claimStatus.estimatedCompletion}</p>
            </div>
          </div>

          <div className="mb-4">
            <div className="flex justify-between text-sm mb-2">
              <span className="text-slate-400">Overall Progress</span>
              <span className="text-white">{claimStatus.overallProgress}%</span>
            </div>
            <Progress value={claimStatus.overallProgress} className="h-3" />
          </div>

          <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-4">
            <p className="text-blue-300 text-sm">
              <strong>Next Step:</strong> Complete remaining C&P examinations. You have 2 exams scheduled in the next
              week.
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Individual Claim Items */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">Claimed Conditions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {claimItems.map((item, index) => (
              <div key={index} className="border border-slate-700 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="text-white font-medium">{item.condition}</h4>
                  <div className="flex items-center space-x-2">
                    {item.priority === "high" && <AlertTriangle className="w-4 h-4 text-red-400" />}
                    {item.status === "Completed" && <CheckCircle className="w-4 h-4 text-green-400" />}
                    {item.status !== "Completed" && <Clock className="w-4 h-4 text-yellow-400" />}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-slate-400">Status</p>
                    <p className={getStatusColor(item.status)}>{item.status}</p>
                  </div>
                  <div>
                    <p className="text-slate-400">Rating</p>
                    <p className="text-white">{item.rating}</p>
                  </div>
                  <div>
                    <p className="text-slate-400">Exam Date</p>
                    <p className="text-white">{item.examDate}</p>
                  </div>
                  <div>
                    <p className="text-slate-400">Exam Type</p>
                    <p className="text-white">{item.examType}</p>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Timeline */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Claim Timeline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {timeline.map((event, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div
                    className={`w-4 h-4 rounded-full mt-1 ${event.completed ? "bg-green-400" : event.current ? "bg-yellow-400" : "bg-slate-600"}`}
                  ></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4
                        className={`font-medium ${event.completed ? "text-green-400" : event.current ? "text-yellow-400" : "text-slate-400"}`}
                      >
                        {event.status}
                      </h4>
                      <span className="text-slate-400 text-xs">{event.date}</span>
                    </div>
                    <p className="text-slate-300 text-sm">{event.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Documents */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Supporting Documents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {documents.map((doc, index) => (
              <div key={index} className="border border-slate-700 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="text-white font-medium text-sm">{doc.name}</h4>
                  <Badge variant={doc.status === "Received" ? "default" : "secondary"} className="text-xs">
                    {doc.status}
                  </Badge>
                </div>
                <p className="text-slate-400 text-xs">{doc.date}</p>
                <Badge variant="outline" className="mt-2 text-xs border-slate-600 text-slate-300">
                  {doc.type}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

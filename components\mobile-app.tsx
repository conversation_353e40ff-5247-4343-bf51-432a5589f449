"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Smartphone, Download, WifiOff, FolderSyncIcon as Sync, <PERSON>, Shield, Users } from "lucide-react"

export function MobileApp() {
  const features = [
    {
      icon: WifiOff,
      title: "Offline Access",
      description: "Access your transition checklist, documents, and progress even without internet connection",
      status: "Available",
    },
    {
      icon: Sync,
      title: "Auto-Sync",
      description: "Automatically sync your progress when connection is restored",
      status: "Available",
    },
    {
      icon: Bell,
      title: "Push Notifications",
      description: "Get reminders for important deadlines and upcoming appointments",
      status: "Available",
    },
    {
      icon: Shield,
      title: "Secure Storage",
      description: "Military-grade encryption for your sensitive documents and data",
      status: "Available",
    },
    {
      icon: Users,
      title: "Family Sharing",
      description: "Share appropriate information with family members and keep them updated",
      status: "Coming Soon",
    },
  ]

  const screenshots = [
    {
      title: "Dashboard",
      description: "Quick overview of your transition progress",
      image: "/placeholder.svg?height=400&width=200",
    },
    {
      title: "Task List",
      description: "Manage your transition tasks on the go",
      image: "/placeholder.svg?height=400&width=200",
    },
    {
      title: "Documents",
      description: "Access important documents offline",
      image: "/placeholder.svg?height=400&width=200",
    },
    {
      title: "Calendar",
      description: "Track appointments and deadlines",
      image: "/placeholder.svg?height=400&width=200",
    },
  ]

  const deploymentFeatures = [
    {
      title: "Deployed Personnel Support",
      description: "Special features for service members in deployed locations",
      features: [
        "Limited bandwidth optimization",
        "Offline document storage",
        "Delayed sync capabilities",
        "Emergency contact integration",
      ],
    },
    {
      title: "Remote Area Access",
      description: "Designed for areas with poor or no internet connectivity",
      features: [
        "Complete offline functionality",
        "Local data storage",
        "Satellite connection support",
        "Low-bandwidth updates",
      ],
    },
  ]

  const stats = [
    { label: "Download Size", value: "45 MB", description: "Optimized for limited storage" },
    { label: "Offline Storage", value: "500 MB", description: "Store documents and data locally" },
    { label: "Battery Usage", value: "Low", description: "Optimized for extended use" },
    { label: "Data Usage", value: "Minimal", description: "Sync only when needed" },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">Mobile App & Offline Access</h1>
        <div className="flex space-x-2">
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Download className="w-4 h-4 mr-2" />
            Download iOS
          </Button>
          <Button className="bg-green-600 hover:bg-green-700">
            <Download className="w-4 h-4 mr-2" />
            Download Android
          </Button>
        </div>
      </div>

      {/* App Overview */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Smartphone className="w-5 h-5 mr-2" />
            MTCC Mobile - Transition Command Center
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <h3 className="text-white font-semibold mb-4">Designed for Military Life</h3>
              <p className="text-slate-300 mb-4">
                Our mobile app is specifically designed for the unique challenges of military service, including
                deployments, remote assignments, and limited connectivity situations.
              </p>
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <WifiOff className="w-5 h-5 text-blue-400" />
                  <span className="text-slate-300">Works completely offline</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Shield className="w-5 h-5 text-green-400" />
                  <span className="text-slate-300">Military-grade security</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Sync className="w-5 h-5 text-purple-400" />
                  <span className="text-slate-300">Smart sync when connected</span>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              {stats.map((stat, index) => (
                <div key={index} className="bg-slate-700 rounded-lg p-4">
                  <h4 className="text-white font-semibold">{stat.label}</h4>
                  <p className="text-2xl font-bold text-blue-400">{stat.value}</p>
                  <p className="text-slate-400 text-sm">{stat.description}</p>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Features */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Key Features</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <div key={index} className="border border-slate-700 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <feature.icon className="w-8 h-8 text-blue-400" />
                  <Badge variant={feature.status === "Available" ? "default" : "secondary"} className="text-xs">
                    {feature.status}
                  </Badge>
                </div>
                <h4 className="text-white font-semibold mb-2">{feature.title}</h4>
                <p className="text-slate-300 text-sm">{feature.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Screenshots */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">App Screenshots</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {screenshots.map((screenshot, index) => (
              <div key={index} className="text-center">
                <div className="bg-slate-700 rounded-lg p-4 mb-3">
                  <div className="w-full h-64 bg-slate-600 rounded-lg flex items-center justify-center">
                    <Smartphone className="w-16 h-16 text-slate-400" />
                  </div>
                </div>
                <h4 className="text-white font-medium mb-1">{screenshot.title}</h4>
                <p className="text-slate-400 text-sm">{screenshot.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Deployment Features */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {deploymentFeatures.map((section, index) => (
          <Card key={index} className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">{section.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-slate-300 mb-4">{section.description}</p>
              <div className="space-y-2">
                {section.features.map((feature, idx) => (
                  <div key={idx} className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span className="text-slate-300 text-sm">{feature}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Download Section */}
      <Card className="bg-gradient-to-r from-blue-900/20 to-purple-900/20 border-blue-700/30">
        <CardContent className="pt-6">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-4">Ready to Take Your Transition Mobile?</h3>
            <p className="text-slate-300 mb-6 max-w-2xl mx-auto">
              Download the MTCC mobile app and have your entire transition command center in your pocket. Perfect for
              deployed personnel and those in remote locations.
            </p>
            <div className="flex justify-center space-x-4">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                <Download className="w-5 h-5 mr-2" />
                Download for iOS
              </Button>
              <Button size="lg" className="bg-green-600 hover:bg-green-700">
                <Download className="w-5 h-5 mr-2" />
                Download for Android
              </Button>
            </div>
            <p className="text-slate-400 text-sm mt-4">
              Available on App Store and Google Play • Free for all military personnel
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

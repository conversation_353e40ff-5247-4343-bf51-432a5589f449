"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Star, Clock, Users, Info } from "lucide-react"

interface PrioritiesProps {
  data: any
  onNext: (data: any) => void
  onBack?: () => void
}

export function Priorities({ data, onNext, onBack }: PrioritiesProps) {
  const [formData, setFormData] = useState({
    topPriorities: data.topPriorities || [],
    timeCommitment: data.timeCommitment || "",
    supportNeeds: data.supportNeeds || [],
    communicationPreference: data.communicationPreference || "",
    familyStatus: data.familyStatus || "",
  })

  const priorityOptions = [
    {
      id: "va-benefits",
      label: "Maximize VA Benefits",
      icon: "🏥",
      description: "Get the highest disability rating and benefits",
    },
    {
      id: "job-search",
      label: "Land a Great Job",
      icon: "💼",
      description: "Find employment that matches your skills and goals",
    },
    {
      id: "financial-security",
      label: "Financial Security",
      icon: "💰",
      description: "Plan for retirement and build wealth",
    },
    { id: "education", label: "Education & Training", icon: "🎓", description: "Use GI Bill benefits strategically" },
    {
      id: "family-transition",
      label: "Family Transition",
      icon: "👨‍👩‍👧‍👦",
      description: "Help family adjust to civilian life",
    },
    { id: "location-move", label: "Relocation", icon: "🏠", description: "Successfully move to your target location" },
    { id: "entrepreneurship", label: "Start a Business", icon: "🚀", description: "Launch your own company" },
    {
      id: "networking",
      label: "Build Network",
      icon: "🤝",
      description: "Connect with other veterans and professionals",
    },
  ]

  const supportOptions = [
    { id: "ai-coaching", label: "AI-Powered Coaching", icon: "🤖" },
    { id: "peer-mentorship", label: "Peer Mentorship", icon: "👥" },
    { id: "expert-guidance", label: "Expert Guidance", icon: "🎯" },
    { id: "family-support", label: "Family Support Resources", icon: "❤️" },
    { id: "community", label: "Veteran Community", icon: "🏛️" },
    { id: "accountability", label: "Accountability Partner", icon: "✅" },
  ]

  const handlePriorityChange = (priorityId: string, checked: boolean) => {
    if (checked && formData.topPriorities.length < 3) {
      setFormData({
        ...formData,
        topPriorities: [...formData.topPriorities, priorityId],
      })
    } else if (!checked) {
      setFormData({
        ...formData,
        topPriorities: formData.topPriorities.filter((id: string) => id !== priorityId),
      })
    }
  }

  const handleSupportChange = (supportId: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        supportNeeds: [...formData.supportNeeds, supportId],
      })
    } else {
      setFormData({
        ...formData,
        supportNeeds: formData.supportNeeds.filter((id: string) => id !== supportId),
      })
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onNext(formData)
  }

  const getSelectedPriorities = () => {
    return priorityOptions.filter((option) => formData.topPriorities.includes(option.id))
  }

  return (
    <div className="space-y-6">
      {/* Purpose Explanation */}
      <Card className="bg-orange-900/20 border-orange-700/30">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <Info className="w-5 h-5 text-orange-400 mt-0.5" />
            <div>
              <h3 className="text-orange-300 font-semibold mb-2">Why We Ask About Your Priorities</h3>
              <p className="text-orange-200 text-sm mb-3">
                Your priorities shape your entire MTCC experience. We use this to:
              </p>
              <ul className="text-orange-200 text-sm space-y-1">
                <li>• Customize your dashboard to focus on what matters most</li>
                <li>• Prioritize tasks and deadlines based on your goals</li>
                <li>• Connect you with the right mentors and resources</li>
                <li>• Send you relevant notifications and reminders</li>
                <li>• Match your time availability with realistic action plans</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Star className="w-5 h-5 mr-2" />
            Your Priorities & Preferences
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label className="text-slate-300 mb-4 block">What are your top 3 priorities? (Select up to 3)</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {priorityOptions.map((priority) => (
                  <div
                    key={priority.id}
                    className={`border rounded-lg p-4 transition-colors ${
                      formData.topPriorities.includes(priority.id)
                        ? "border-blue-500 bg-blue-900/20"
                        : "border-slate-700 hover:border-slate-600"
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <Checkbox
                        id={priority.id}
                        checked={formData.topPriorities.includes(priority.id)}
                        onCheckedChange={(checked) => handlePriorityChange(priority.id, checked as boolean)}
                        disabled={!formData.topPriorities.includes(priority.id) && formData.topPriorities.length >= 3}
                        className="border-slate-600 mt-1"
                      />
                      <div className="flex-1">
                        <Label htmlFor={priority.id} className="text-slate-300 cursor-pointer flex items-center">
                          <span className="mr-2 text-lg">{priority.icon}</span>
                          {priority.label}
                        </Label>
                        <p className="text-slate-400 text-xs mt-1">{priority.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <p className="text-slate-400 text-xs mt-2">💡 Your dashboard will be organized around these priorities</p>
            </div>

            {/* Priority Preview */}
            {formData.topPriorities.length > 0 && (
              <Card className="bg-blue-900/20 border-blue-700/30">
                <CardContent className="pt-4">
                  <h4 className="text-blue-300 font-medium mb-2">🎯 Your Personalized Focus Areas:</h4>
                  <div className="flex flex-wrap gap-2">
                    {getSelectedPriorities().map((priority) => (
                      <Badge key={priority.id} variant="outline" className="border-blue-600 text-blue-400">
                        {priority.icon} {priority.label}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="timeCommitment" className="text-slate-300 flex items-center mb-2">
                  <Clock className="w-4 h-4 mr-2" />
                  How much time can you dedicate weekly? *
                </Label>
                <Select
                  value={formData.timeCommitment}
                  onValueChange={(value) => setFormData({ ...formData, timeCommitment: value })}
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue placeholder="Select time commitment" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="1-2-hours">1-2 hours per week</SelectItem>
                    <SelectItem value="3-5-hours">3-5 hours per week</SelectItem>
                    <SelectItem value="6-10-hours">6-10 hours per week</SelectItem>
                    <SelectItem value="10-plus-hours">10+ hours per week</SelectItem>
                    <SelectItem value="varies">Varies by week</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-slate-400 text-xs mt-1">
                  💡 We'll create a realistic timeline based on your availability
                </p>
              </div>

              <div>
                <Label htmlFor="communicationPreference" className="text-slate-300 mb-2 block">
                  Preferred communication style
                </Label>
                <Select
                  value={formData.communicationPreference}
                  onValueChange={(value) => setFormData({ ...formData, communicationPreference: value })}
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue placeholder="Select preference" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="daily-reminders">Daily reminders</SelectItem>
                    <SelectItem value="weekly-summaries">Weekly summaries</SelectItem>
                    <SelectItem value="milestone-updates">Milestone updates only</SelectItem>
                    <SelectItem value="minimal">Minimal notifications</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label className="text-slate-300 mb-4 block flex items-center">
                <Users className="w-4 h-4 mr-2" />
                What type of support would be most helpful?
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {supportOptions.map((support) => (
                  <div key={support.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={support.id}
                      checked={formData.supportNeeds.includes(support.id)}
                      onCheckedChange={(checked) => handleSupportChange(support.id, checked as boolean)}
                      className="border-slate-600"
                    />
                    <Label htmlFor={support.id} className="text-slate-300 text-sm cursor-pointer flex items-center">
                      <span className="mr-2">{support.icon}</span>
                      {support.label}
                    </Label>
                  </div>
                ))}
              </div>
              <p className="text-slate-400 text-xs mt-2">
                💡 We'll connect you with these support resources and features
              </p>
            </div>

            <div>
              <Label htmlFor="familyStatus" className="text-slate-300 mb-2 block">
                Family situation (helps us provide relevant resources)
              </Label>
              <Select
                value={formData.familyStatus}
                onValueChange={(value) => setFormData({ ...formData, familyStatus: value })}
              >
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="Select family status" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  <SelectItem value="single">Single, no dependents</SelectItem>
                  <SelectItem value="married-no-kids">Married, no children</SelectItem>
                  <SelectItem value="married-with-kids">Married with children</SelectItem>
                  <SelectItem value="single-parent">Single parent</SelectItem>
                  <SelectItem value="caring-for-parents">Caring for elderly parents</SelectItem>
                  <SelectItem value="other">Other family situation</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-slate-400 text-xs mt-1">💡 We'll show family-specific resources and considerations</p>
            </div>

            <div className="flex justify-between pt-6">
              {onBack && (
                <Button type="button" variant="outline" onClick={onBack} className="border-slate-600 text-slate-300">
                  Back
                </Button>
              )}
              <Button type="submit" className="bg-blue-600 hover:bg-blue-700 ml-auto">
                Continue
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Military Theme Global Styles */
:root {
  --military-primary: #3b82f6;
  --military-secondary: #1e40af;
  --military-accent: #ef4444;
  --military-neutral-900: #171717;
  --military-neutral-800: #262626;
  --military-neutral-700: #404040;
  --military-neutral-600: #525252;
  --military-neutral-500: #737373;
  --military-neutral-400: #a3a3a3;
  --military-neutral-300: #d4d4d4;
  --military-neutral-200: #e5e5e5;
  --military-neutral-100: #f5f5f5;
}

body {
  font-family: 'Inter', Arial, Helvetica, sans-serif;
  background-color: var(--military-neutral-900);
  color: var(--military-neutral-100);
}

.font-inter {
  font-family: 'Inter', sans-serif;
}

.font-montserrat {
  font-family: 'Montserrat', sans-serif;
}

/* Military Theme Animations */
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Animation Delay Classes */
.animate-delayed-1 { animation: fadeInUp 0.8s 0.1s both; }
.animate-delayed-2 { animation: fadeInUp 0.8s 0.25s both; }
.animate-delayed-3 { animation: fadeInUp 0.8s 0.4s both; }
.animate-delayed-4 { animation: fadeInUp 0.8s 0.55s both; }
.animate-delayed-5 { animation: fadeInUp 0.8s 0.7s both; }
.animate-slide-left { animation: slideInLeft 0.6s ease-out; }

/* Glass Morphism Effects */
.glass-card {
  background: rgba(38, 38, 38, 0.8);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(64, 64, 64, 0.3);
  border-radius: 12px;
}

.glass-sidebar {
  background: rgba(23, 23, 23, 0.9);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(64, 64, 64, 0.3);
}

.glass-button {
  background: rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.5);
}

/* Military Badge Styles */
.military-badge {
  background: linear-gradient(135deg, var(--military-primary), var(--military-secondary));
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.military-badge-danger {
  background: linear-gradient(135deg, var(--military-accent), #dc2626);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--military-neutral-800);
}

::-webkit-scrollbar-thumb {
  background: var(--military-neutral-600);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--military-neutral-500);
}

/* Toggle Switch Styles */
.toggle {
  width: 2.75rem;
  height: 1.5rem;
  background: var(--military-neutral-600);
  border-radius: 9999px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.toggle.checked {
  background: var(--military-primary);
}

.toggle-dot {
  position: absolute;
  top: 0.125rem;
  left: 0.125rem;
  width: 1.25rem;
  height: 1.25rem;
  background: white;
  border-radius: 50%;
  transition: transform 0.25s ease;
}

.toggle.checked .toggle-dot {
  transform: translateX(1.25rem);
}

/* Focus States */
.focus-military:focus {
  outline: none;
  ring: 2px;
  ring-color: var(--military-primary);
  ring-opacity: 0.5;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

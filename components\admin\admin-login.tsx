"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Shield, Lock, Eye, EyeOff } from "lucide-react"

interface AdminLoginProps {
  onLogin: (adminData: any) => void
}

export function AdminLogin({ onLogin }: AdminLoginProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    accessCode: "",
  })
  const [error, setError] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    setError("")

    // Admin credentials (in production, this would be server-side validation)
    const validCredentials = {
      username: "admin",
      password: "mtcc2024!",
      accessCode: "COMMAND",
    }

    if (
      formData.username === validCredentials.username &&
      formData.password === validCredentials.password &&
      formData.accessCode === validCredentials.accessCode
    ) {
      onLogin({
        username: formData.username,
        role: "admin",
        permissions: ["view_all_users", "edit_settings", "manage_themes", "system_admin"],
        loginTime: new Date().toISOString(),
      })
    } else {
      setError("Invalid credentials. Access denied.")
    }
  }

  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-red-600 rounded-lg flex items-center justify-center mx-auto mb-4">
            <Shield className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">MTCC Admin</h1>
          <p className="text-slate-400">System Administration Portal</p>
        </div>

        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white text-center flex items-center justify-center">
              <Lock className="w-5 h-5 mr-2" />
              Secure Access Required
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="username" className="text-slate-300">
                  Administrator Username
                </Label>
                <Input
                  id="username"
                  value={formData.username}
                  onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                  className="bg-slate-700 border-slate-600 text-white"
                  placeholder="Enter admin username"
                  required
                />
              </div>

              <div>
                <Label htmlFor="password" className="text-slate-300">
                  Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                    className="bg-slate-700 border-slate-600 text-white pr-10"
                    placeholder="Enter password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-300"
                  >
                    {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              <div>
                <Label htmlFor="accessCode" className="text-slate-300">
                  Access Code
                </Label>
                <Input
                  id="accessCode"
                  value={formData.accessCode}
                  onChange={(e) => setFormData({ ...formData, accessCode: e.target.value.toUpperCase() })}
                  className="bg-slate-700 border-slate-600 text-white"
                  placeholder="Enter access code"
                  required
                />
              </div>

              {error && (
                <div className="bg-red-900/20 border border-red-700/30 rounded-lg p-3">
                  <p className="text-red-400 text-sm">{error}</p>
                </div>
              )}

              <Button type="submit" className="w-full bg-red-600 hover:bg-red-700">
                Access Admin Panel
              </Button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-slate-400 text-xs">🔒 Authorized personnel only</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

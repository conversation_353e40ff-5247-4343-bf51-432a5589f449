"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Check<PERSON>ircle, Clock, Info } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"

export function StatsCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Overall Progress */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-slate-200">Overall Progress</CardTitle>
          <Info className="h-4 w-4 text-slate-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-white mb-2">33%</div>
          <Progress value={33} className="mb-2" />
          <p className="text-xs text-slate-400">4 of 12 tasks completed</p>
        </CardContent>
      </Card>

      {/* Active Tasks */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-slate-200">Active Tasks</CardTitle>
          <Clock className="h-4 w-4 text-blue-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-white mb-2">5</div>
          <p className="text-xs text-slate-400">Tasks currently in progress</p>
        </CardContent>
      </Card>

      {/* Overdue Items */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-slate-200">Overdue Items</CardTitle>
          <AlertTriangle className="h-4 w-4 text-red-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-white mb-2">2</div>
          <p className="text-xs text-slate-400">Items requiring immediate attention</p>
        </CardContent>
      </Card>

      {/* Days Until Separation */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-slate-200">Days Until Separation</CardTitle>
          <Calendar className="h-4 w-4 text-slate-400" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-400 mb-2">Past Due</div>
          <p className="text-xs text-slate-400">Separation date has passed</p>
        </CardContent>
      </Card>
    </div>
  )
}

export function UpcomingTasks() {
  const tasks = [
    {
      title: "Schedule Separation History and Physical Examination (SHPE)",
      description: "Schedule your SHPE, which should be conducted between 90 and 180 days before your retirement date.",
      timeframe: "6-12 Months Prior",
      category: "Medical",
      status: "Not Started",
      priority: "high",
      dueDate: "Aug 14, 2024",
    },
    {
      title: "Update Resume and LinkedIn Profile",
      description: "Ensure your professional profiles reflect your military experience and target civilian roles.",
      timeframe: "6-12 Months Prior",
      category: "Career",
      status: "In Progress",
      priority: "medium",
      dueDate: "Jul 29, 2024",
    },
    {
      title: "Research Target Companies",
      description: "Identify and research companies in your target industry and location.",
      timeframe: "6-12 Months Prior",
      category: "Career",
      status: "Not Started",
      priority: "medium",
      dueDate: "Jul 31, 2024",
    },
  ]

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          Upcoming Tasks
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {tasks.map((task, index) => (
          <div key={index} className="border border-slate-700 rounded-lg p-4">
            <div className="flex items-start justify-between mb-2">
              <h4 className="text-white font-medium text-sm">{task.title}</h4>
              <div className="flex items-center space-x-2">
                {task.priority === "high" && <AlertTriangle className="w-4 h-4 text-red-400" />}
                <CheckCircle className="w-4 h-4 text-green-400" />
              </div>
            </div>
            <p className="text-slate-400 text-xs mb-3">{task.description}</p>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                  {task.timeframe}
                </Badge>
                <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                  {task.category}
                </Badge>
                <Badge variant={task.status === "In Progress" ? "default" : "secondary"} className="text-xs">
                  {task.status}
                </Badge>
              </div>
              <span className="text-slate-400 text-xs">Due: {task.dueDate}</span>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

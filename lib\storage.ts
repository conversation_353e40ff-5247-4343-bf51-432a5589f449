export class OnboardingStorage {
  private static STORAGE_KEY = "mtcc_onboarding_state"
  private static COMPLETED_KEY = "mtcc_onboarding_completed"

  static saveState(step: number, data: any) {
    const state = {
      currentStep: step,
      userData: data,
      timestamp: new Date().toISOString(),
      version: "1.0",
    }
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(state))
  }

  static loadState() {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY)
      if (saved) {
        const state = JSON.parse(saved)
        // Check if state is less than 7 days old
        const savedDate = new Date(state.timestamp)
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)

        if (savedDate > weekAgo) {
          return state
        }
      }
    } catch (error) {
      console.error("Error loading onboarding state:", error)
    }
    return null
  }

  static clearState() {
    localStorage.removeItem(this.STORAGE_KEY)
    localStorage.removeItem(this.COMPLETED_KEY)
  }

  static markCompleted() {
    localStorage.setItem(this.COMPLETED_KEY, "true")
    this.clearState()
  }

  static isCompleted() {
    return localStorage.getItem(this.COMPLETED_KEY) === "true"
  }

  static hasInProgressState() {
    return this.loadState() !== null
  }
}

export class AdminStorage {
  private static ADMIN_KEY = "mtcc_admin_session"

  static saveAdminSession(adminData: any) {
    localStorage.setItem(
      this.ADMIN_KEY,
      JSON.stringify({
        ...adminData,
        timestamp: new Date().toISOString(),
      }),
    )
  }

  static getAdminSession() {
    try {
      const saved = localStorage.getItem(this.ADMIN_KEY)
      if (saved) {
        const session = JSON.parse(saved)
        // Check if session is less than 24 hours old
        const savedDate = new Date(session.timestamp)
        const dayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)

        if (savedDate > dayAgo) {
          return session
        }
      }
    } catch (error) {
      console.error("Error loading admin session:", error)
    }
    return null
  }

  static clearAdminSession() {
    localStorage.removeItem(this.ADMIN_KEY)
  }
}

"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Progress } from "@/components/ui/progress"
import { AlertTriangle, CheckCircle, Calendar, FileText, DollarSign, Heart, Briefcase } from "lucide-react"
import { useState } from "react"

interface TimelineProps {
  user?: {
    rank?: string
    separationDate?: string
    separationType?: string
  }
}

export function TransitionTimeline({ user }: TimelineProps) {
  const [completedTasks, setCompletedTasks] = useState<Set<string>>(new Set())

  const toggleTask = (taskId: string) => {
    const newCompleted = new Set(completedTasks)
    if (newCompleted.has(taskId)) {
      newCompleted.delete(taskId)
    } else {
      newCompleted.add(taskId)
    }
    setCompletedTasks(newCompleted)
  }

  // Determine if user is enlisted or officer
  const isOfficer =
    user?.rank?.includes("O-") ||
    ["Lieutenant", "Captain", "Major", "Colonel", "General"].some((rank) => user?.rank?.includes(rank))

  // Calculate days until separation
  const daysUntilSeparation = user?.separationDate
    ? Math.ceil((new Date(user.separationDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    : 365

  const getTimelinePhases = () => {
    if (isOfficer) {
      return [
        {
          title: "Early Planning Phase",
          timeframe: "24-12 Months Prior",
          description: "Initial transition planning and preparation",
          status: daysUntilSeparation <= 365 ? "completed" : "upcoming",
          icon: <Calendar className="w-4 h-4" />,
          color: "bg-blue-500",
          tasks: [
            { id: "tap-initial", text: "Attend TAP Initial Counseling", critical: true },
            { id: "itp-start", text: "Start Individual Transition Plan", critical: true },
            { id: "financial-planning", text: "Begin financial planning", critical: false },
          ],
        },
        {
          title: "Active Preparation",
          timeframe: "12-6 Months Prior",
          description: "Submit retirement request and begin formal preparation",
          status:
            daysUntilSeparation <= 365 && daysUntilSeparation > 180
              ? "current"
              : daysUntilSeparation <= 180
                ? "completed"
                : "upcoming",
          icon: <FileText className="w-4 h-4" />,
          color: "bg-yellow-500",
          tasks: [
            { id: "retirement-request", text: "Submit retirement request via NSIPS RnS", critical: true },
            { id: "dav-schedule", text: "Schedule DAV appointment (at 6-month mark)", critical: true },
            { id: "medical-records", text: "Collect medical records and documents", critical: true },
            { id: "resume-building", text: "Begin resume building and job search", critical: false },
            { id: "networking", text: "Start professional networking", critical: false },
          ],
        },
        {
          title: "Documentation Phase",
          timeframe: "9-5 Months Prior",
          description: "Submit critical paperwork and prepare documentation",
          status:
            daysUntilSeparation <= 270 && daysUntilSeparation > 150
              ? "current"
              : daysUntilSeparation <= 150
                ? "completed"
                : "upcoming",
          icon: <FileText className="w-4 h-4" />,
          color: "bg-orange-500",
          tasks: [
            { id: "retirement-orders", text: "Submit retirement orders to TSC", critical: true },
            { id: "separation-questionnaire", text: "Submit NPPSC 1900/1 (Separation Questionnaire)", critical: true },
            { id: "vmet-jst", text: "Prepare DD-2586 (VMET) and JST", critical: true },
            { id: "ptdy-orders", text: "Submit PTDY orders & leave request", critical: false },
          ],
        },
        {
          title: "Final Preparations",
          timeframe: "90 Days Prior",
          description: "Complete final requirements and finalize transition",
          status: daysUntilSeparation <= 90 ? "current" : "upcoming",
          icon: <CheckCircle className="w-4 h-4" />,
          color: "bg-green-500",
          tasks: [
            { id: "capstone", text: "Complete Capstone", critical: true },
            { id: "dd2656-dfas", text: "Submit DD-2656 to DFAS (45-60 days prior)", critical: true },
            { id: "dd2648", text: "Complete DD-2648 (Pre-Separation Checklist)", critical: true },
            { id: "checkout-sheet", text: "Finalize command check-out sheet", critical: true },
            { id: "pcs-travel", text: "Submit PCS travel request (NAVPERS 7041/1)", critical: false },
            { id: "medical-dental", text: "Medical/Dental endorsements", critical: true },
            { id: "fleet-reserve", text: "Fleet Reserve Checklist (NPPSC 1800/1)", critical: false },
          ],
        },
      ]
    } else {
      // Enlisted timeline
      return [
        {
          title: "Initial Planning",
          timeframe: "18-12 Months Prior",
          description: "Begin career exploration and initial transition planning",
          status: daysUntilSeparation <= 540 ? "completed" : "upcoming",
          icon: <Calendar className="w-4 h-4" />,
          color: "bg-blue-500",
          tasks: [
            {
              id: "retirement-window",
              text: `Submit retirement request (${user?.rank?.includes("E-6") ? "6-18 months" : "6-24 months"} window)`,
              critical: true,
            },
            { id: "itp-begin", text: "Begin Individual Transition Plan", critical: true },
            { id: "ebenefits", text: "Register for eBenefits", critical: true },
          ],
        },
        {
          title: "Financial & Healthcare Prep",
          timeframe: "12-6 Months Prior",
          description: "Secure benefits and begin formal preparation",
          status:
            daysUntilSeparation <= 365 && daysUntilSeparation > 180
              ? "current"
              : daysUntilSeparation <= 180
                ? "completed"
                : "upcoming",
          icon: <DollarSign className="w-4 h-4" />,
          color: "bg-yellow-500",
          tasks: [
            { id: "retirement-budget", text: "Create a retirement budget", critical: true },
            { id: "income-analysis", text: "Analyze post-retirement income vs. expenses", critical: true },
            { id: "financial-counselor", text: "Schedule session with financial counselor", critical: false },
            { id: "va-healthcare", text: "Register for VA Healthcare (requires DD214)", critical: true },
            { id: "tricare-plan", text: "Choose TRICARE or supplemental plan", critical: true },
            { id: "medical-exams", text: "Schedule final medical/dental exams", critical: true },
            { id: "prescriptions", text: "Refill 90-day supply of prescriptions", critical: false },
          ],
        },
        {
          title: "Claims & Career Transition",
          timeframe: "6 Months Prior",
          description: "Begin VA claims and career preparation",
          status:
            daysUntilSeparation <= 180 && daysUntilSeparation > 90
              ? "current"
              : daysUntilSeparation <= 90
                ? "completed"
                : "upcoming",
          icon: <Heart className="w-4 h-4" />,
          color: "bg-orange-500",
          tasks: [
            { id: "dav-appointment", text: "Schedule DAV appointment", critical: true },
            { id: "va-disability", text: "Begin VA disability claim", critical: true },
            { id: "medical-documentation", text: "Gather supporting medical documentation", critical: true },
            { id: "estate-planning", text: "Start/Update estate planning", critical: false },
            { id: "tap-curriculum", text: "Attend TAP curriculum", critical: true },
            { id: "resume-update", text: "Update resume", critical: true },
            { id: "job-exploration", text: "Explore job opportunities", critical: false },
            { id: "counselors", text: "Meet with employment/education counselors", critical: false },
          ],
        },
        {
          title: "Final 90-60 Days",
          timeframe: "90-60 Days Prior",
          description: "Finalize benefits and complete administrative tasks",
          status:
            daysUntilSeparation <= 90 && daysUntilSeparation > 60
              ? "current"
              : daysUntilSeparation <= 60
                ? "completed"
                : "upcoming",
          icon: <FileText className="w-4 h-4" />,
          color: "bg-red-500",
          tasks: [
            { id: "sbp-election", text: "Finalize SBP election", critical: true },
            { id: "dd2656-submission", text: "Submit DD2656 (Survivor Benefit Plan) to DFAS", critical: true },
            { id: "va-claim-finalize", text: "Finalize VA claim paperwork with DAV", critical: true },
            { id: "separation-leave", text: "Submit separation leave/PTDY", critical: false },
            { id: "dd2648-complete", text: "Complete DD-2648 (Pre-Separation Counseling)", critical: true },
          ],
        },
        {
          title: "Final 60 Days",
          timeframe: "Final 60 Days",
          description: "Complete all final requirements",
          status: daysUntilSeparation <= 60 ? "current" : "upcoming",
          icon: <CheckCircle className="w-4 h-4" />,
          color: "bg-green-500",
          tasks: [
            { id: "dd214-review", text: "Review DD214 draft", critical: true },
            {
              id: "navpers-submission",
              text: "Submit NAVPERS 7041/1, NPPSC 1900/1, NPPSC 1800/1 to TSC",
              critical: true,
            },
            { id: "tricare-transition", text: "Finalize TRICARE transition", critical: true },
            { id: "household-goods", text: "Confirm final household goods move with DoD", critical: false },
            { id: "capstone-complete", text: "Complete Capstone appointment", critical: true },
            { id: "contact-update", text: "Update address, direct deposit, and contact info", critical: true },
          ],
        },
      ]
    }
  }

  const phases = getTimelinePhases()
  const totalTasks = phases.reduce((sum, phase) => sum + phase.tasks.length, 0)
  const completedCount = completedTasks.size
  const progressPercentage = (completedCount / totalTasks) * 100

  return (
    <div className="space-y-6">
      {/* Header with Progress */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              {isOfficer ? "Officer" : "Enlisted"} Retirement Timeline
            </CardTitle>
            <Badge variant="outline" className="border-blue-500 text-blue-400">
              {daysUntilSeparation} days remaining
            </Badge>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-slate-300">
                Progress: {completedCount}/{totalTasks} tasks
              </span>
              <span className="text-slate-300">{Math.round(progressPercentage)}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
        </CardHeader>
      </Card>

      {/* Critical Reminder */}
      <Card className="bg-red-900/20 border-red-700/30">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5" />
            <div>
              <h3 className="text-red-300 font-semibold mb-2">⚠️ Critical Reminder</h3>
              <ul className="text-red-200 text-sm space-y-1">
                <li>
                  • <strong>Don't wait to file VA claims</strong> - Start at the 6-month mark
                </li>
                <li>
                  • <strong>DAV appointments fill up fast</strong> - Schedule early
                </li>
                <li>
                  • <strong>Early filing helps avoid delays</strong> in VA disability compensation
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timeline Phases */}
      <div className="space-y-4">
        {phases.map((phase, index) => (
          <Card key={index} className="bg-slate-800 border-slate-700">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full ${phase.color} flex items-center justify-center text-white`}>
                    {phase.icon}
                  </div>
                  <div>
                    <h4 className="text-white font-medium">{phase.title}</h4>
                    <p className="text-slate-400 text-sm">{phase.timeframe}</p>
                  </div>
                </div>
                <Badge
                  variant={phase.status === "current" ? "default" : "outline"}
                  className={
                    phase.status === "completed"
                      ? "border-green-500 text-green-400"
                      : phase.status === "current"
                        ? "bg-yellow-600"
                        : "border-slate-600 text-slate-400"
                  }
                >
                  {phase.status === "completed" ? "Completed" : phase.status === "current" ? "Current" : "Upcoming"}
                </Badge>
              </div>
              <p className="text-slate-400 text-sm">{phase.description}</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {phase.tasks.map((task, taskIndex) => (
                  <div key={task.id} className="flex items-start space-x-3">
                    <Checkbox
                      id={task.id}
                      checked={completedTasks.has(task.id)}
                      onCheckedChange={() => toggleTask(task.id)}
                      className="mt-0.5"
                    />
                    <label
                      htmlFor={task.id}
                      className={`text-sm cursor-pointer flex-1 ${
                        completedTasks.has(task.id) ? "line-through text-slate-500" : "text-slate-300"
                      }`}
                    >
                      {task.text}
                      {task.critical && (
                        <Badge variant="destructive" className="ml-2 text-xs">
                          Critical
                        </Badge>
                      )}
                    </label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <FileText className="w-4 h-4 mr-2" />
              Download Checklist
            </Button>
            <Button variant="outline" className="border-slate-600 text-slate-300">
              <Calendar className="w-4 h-4 mr-2" />
              Set Reminders
            </Button>
            <Button variant="outline" className="border-slate-600 text-slate-300">
              <Briefcase className="w-4 h-4 mr-2" />
              Find Resources
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

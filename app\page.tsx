"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "../components/auth/login"
import { AdminLogin } from "../components/admin/admin-login"
import { AdminDashboard } from "../components/admin/admin-dashboard"
import { OnboardingFlow } from "../components/onboarding/onboarding-flow"
import RetirementDashboard from "../retirement-dashboard"
import { OnboardingStorage, AdminStorage } from "../lib/storage"
import { Shield } from "react-feather" // Import the Shield component

export default function Page() {
  const [user, setUser] = useState<any>(null)
  const [admin, setAdmin] = useState<any>(null)
  const [showOnboarding, setShowOnboarding] = useState(false)
  const [showAdminLogin, setShowAdminLogin] = useState(false)

  useEffect(() => {
    // Check for existing admin session
    const adminSession = AdminStorage.getAdminSession()
    if (adminSession) {
      setAdmin(adminSession)
    }
  }, [])

  const handleLogin = (userData: any) => {
    if (userData.isNewUser && !OnboardingStorage.isCompleted()) {
      setShowOnboarding(true)
      setUser(userData)
    } else {
      // Existing user or completed onboarding, go straight to dashboard
      setUser({ ...userData, onboarded: true })
    }
  }

  const handleAdminLogin = (adminData: any) => {
    AdminStorage.saveAdminSession(adminData)
    setAdmin(adminData)
    setShowAdminLogin(false)
  }

  const handleOnboardingComplete = (completeUserData: any) => {
    OnboardingStorage.markCompleted()
    setUser({ ...completeUserData, onboarded: true })
    setShowOnboarding(false)
  }

  const handleAdminLogout = () => {
    setAdmin(null)
  }

  const handleUserLogout = () => {
    setUser(null)
    OnboardingStorage.clearState()
  }

  // Admin interface
  if (admin) {
    return <AdminDashboard admin={admin} onLogout={handleAdminLogout} />
  }

  // Admin login
  if (showAdminLogin) {
    return <AdminLogin onLogin={handleAdminLogin} />
  }

  // User onboarding
  if (showOnboarding) {
    return <OnboardingFlow onComplete={handleOnboardingComplete} />
  }

  // User dashboard
  if (user) {
    return <RetirementDashboard user={user} />
  }

  // Main login screen
  return (
    <div className="relative">
      <Login onLogin={handleLogin} onSignup={() => {}} />

      {/* Admin Access Button */}
      <button
        onClick={() => setShowAdminLogin(true)}
        className="fixed bottom-4 right-4 w-12 h-12 bg-red-600 hover:bg-red-700 rounded-full flex items-center justify-center text-white opacity-50 hover:opacity-100 transition-opacity"
        title="Admin Access"
      >
        <Shield className="w-5 h-5" />
      </button>
    </div>
  )
}

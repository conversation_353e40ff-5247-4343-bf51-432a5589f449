"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { FileText, Calendar, Plus, Upload, UserPlus, Settings } from "lucide-react"

export function RecentDocuments() {
  const documents = [
    { name: "DD-214.pdf", category: "Military Records" },
    { name: "Medical_Records.pdf", category: "Medical" },
    { name: "Performance_Evaluations.pdf", category: "Career" },
  ]

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <FileText className="w-5 h-5 mr-2" />
          Recent Documents
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {documents.map((doc, index) => (
          <div key={index} className="flex items-center justify-between p-2 rounded border border-slate-700">
            <div>
              <div className="text-white text-sm">{doc.name}</div>
            </div>
            <span className="text-slate-400 text-xs">{doc.category}</span>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

export function UpcomingEvents() {
  const events = [
    { name: "TAP Workshop", date: "7/5/2024" },
    { name: "VA Appointment", date: "7/11/2024" },
    { name: "Job Fair", date: "8/4/2024" },
  ]

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <Calendar className="w-5 h-5 mr-2" />
          Upcoming Events
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {events.map((event, index) => (
          <div key={index} className="flex items-center justify-between p-2 rounded border border-slate-700">
            <div className="text-white text-sm">{event.name}</div>
            <span className="text-slate-400 text-xs">{event.date}</span>
          </div>
        ))}
      </CardContent>
    </Card>
  )
}

export function QuickActions() {
  const actions = [
    { name: "Add New Task", icon: Plus },
    { name: "Upload Document", icon: Upload },
    { name: "Schedule Event", icon: Calendar },
    { name: "Update Profile", icon: Settings },
  ]

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <UserPlus className="w-5 h-5 mr-2" />
          Quick Actions
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {actions.map((action, index) => (
          <Button
            key={index}
            variant="outline"
            className="w-full justify-start border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            <action.icon className="w-4 h-4 mr-2" />
            {action.name}
          </Button>
        ))}
      </CardContent>
    </Card>
  )
}

"use client"

import { TransitionTimeline } from "./timeline"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Download, Calendar, Bell, BookOpen } from "lucide-react"

interface TimelinePageProps {
  user: any
}

export function RetirementTimelinePage({ user }: TimelinePageProps) {
  const isOfficer =
    user?.rank?.includes("O-") ||
    ["Lieutenant", "Captain", "Major", "Colonel", "General"].some((rank) => user?.rank?.includes(rank))

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Retirement Timeline</h1>
          <p className="text-slate-400 mt-2">
            Your personalized {isOfficer ? "officer" : "enlisted"} retirement checklist and timeline
          </p>
        </div>
        <div className="flex space-x-3">
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Download className="w-4 h-4 mr-2" />
            Export PDF
          </Button>
          <Button variant="outline" className="border-slate-600 text-slate-300">
            <Bell className="w-4 h-4 mr-2" />
            Set Reminders
          </Button>
        </div>
      </div>

      {/* Key Information Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white text-lg">Retirement Window</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-slate-300">
                <strong>{isOfficer ? "Officers" : `${user?.rank || "E-6 to E-9"}`}:</strong>
              </p>
              <p className="text-slate-400 text-sm">
                {isOfficer
                  ? "Submit 6-18 months before desired date (PERS allows up to 18 months ahead)"
                  : user?.rank?.includes("E-6")
                    ? "Submit 6-18 months before desired date"
                    : "Submit 6-24 months before desired date"}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white text-lg">Critical Deadlines</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Badge variant="destructive" className="mb-2">
                6 Months Out
              </Badge>
              <ul className="text-slate-400 text-sm space-y-1">
                <li>• Schedule DAV appointment</li>
                <li>• Begin VA disability claim</li>
                <li>• Register for VA Healthcare</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white text-lg">Resources</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button variant="ghost" className="w-full justify-start text-slate-300 hover:text-white">
                <BookOpen className="w-4 h-4 mr-2" />
                MILPERSMAN 1810-020
              </Button>
              <Button variant="ghost" className="w-full justify-start text-slate-300 hover:text-white">
                <Calendar className="w-4 h-4 mr-2" />
                TAP Schedule
              </Button>
              <Button variant="ghost" className="w-full justify-start text-slate-300 hover:text-white">
                <Download className="w-4 h-4 mr-2" />
                Forms Library
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Timeline */}
      <TransitionTimeline user={user} />
    </div>
  )
}

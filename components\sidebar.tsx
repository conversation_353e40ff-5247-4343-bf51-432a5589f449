import { Bar<PERSON><PERSON>, Calendar, CreditCard, Gauge, Home } from "lucide-react"
import Link from "next/link"

const navigationItems = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: <Home className="w-4 h-4" />,
    description: "Your personal overview",
  },
  {
    id: "finances",
    label: "Finances",
    icon: <CreditCard className="w-4 h-4" />,
    description: "Manage your accounts and transactions",
  },
  {
    id: "investments",
    label: "Investments",
    icon: <BarChart className="w-4 h-4" />,
    description: "Track your investment portfolio",
  },
  {
    id: "retirement",
    label: "Retirement Planning",
    icon: <Gauge className="w-4 h-4" />,
    description: "Plan for your future",
  },
  {
    id: "timeline",
    label: "Retirement Timeline",
    icon: <Calendar className="w-4 h-4" />,
    description: "Your personalized retirement checklist",
  },
]

const Sidebar = () => {
  return (
    <div className="w-64 flex-shrink-0 border-r bg-gray-50 dark:bg-gray-900 dark:border-gray-700">
      <div className="h-full px-3 py-4 overflow-y-auto bg-gray-50 dark:bg-gray-900">
        <ul className="space-y-2 font-medium">
          {navigationItems.map((item) => (
            <li key={item.id}>
              <Link
                href={`/${item.id}`}
                className="flex items-center p-2 text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group"
              >
                {item.icon}
                <span className="ml-3">{item.label}</span>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}

export default Sidebar

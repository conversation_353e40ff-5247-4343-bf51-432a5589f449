"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON><PERSON>riangle, RotateCcw, Play } from "lucide-react"
import { OnboardingStorage } from "@/lib/storage"
import { BasicInfo } from "./steps/basic-info"
import { MilitaryBackground } from "./steps/military-background"
import { TransitionGoals } from "./steps/transition-goals"
import { CurrentStatus } from "./steps/current-status"
import { Priorities } from "./steps/priorities"
import { FinalSetup } from "./steps/final-setup"

interface OnboardingFlowProps {
  onComplete: (userData: any) => void
}

export function OnboardingFlow({ onComplete }: OnboardingFlowProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [userData, setUserData] = useState({
    // Basic Info
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    currentLocation: "",

    // Military Background
    branch: "",
    rank: "",
    mos: "",
    yearsOfService: "",
    separationDate: "",
    separationType: "",
    clearanceLevel: "",

    // Transition Goals
    targetLocation: "",
    targetIndustry: "",
    salaryRange: "",
    jobType: "",
    educationGoals: "",
    careerTimeline: "",

    // Current Status
    transitionPhase: "",
    completedTasks: [],
    urgentNeeds: [],
    timeUntilSeparation: "",

    // Priorities
    topPriorities: [],
    timeCommitment: "",
    supportNeeds: [],
    communicationPreference: "",
    familyStatus: "",
  })
  const [showResumeOption, setShowResumeOption] = useState(false)
  const [savedState, setSavedState] = useState<any>(null)

  const steps = [
    { title: "Basic Information", component: BasicInfo },
    { title: "Military Background", component: MilitaryBackground },
    { title: "Transition Goals", component: TransitionGoals },
    { title: "Current Status", component: CurrentStatus },
    { title: "Priorities & Focus", component: Priorities },
    { title: "Final Setup", component: FinalSetup },
  ]

  useEffect(() => {
    // Check for saved state on component mount
    const saved = OnboardingStorage.loadState()
    if (saved && saved.currentStep < steps.length) {
      setSavedState(saved)
      setShowResumeOption(true)
    }
  }, [])

  useEffect(() => {
    // Auto-save state whenever userData or currentStep changes
    if (
      currentStep > 0 ||
      Object.values(userData).some((value) => (Array.isArray(value) ? value.length > 0 : value !== ""))
    ) {
      OnboardingStorage.saveState(currentStep, userData)
    }
  }, [currentStep, userData])

  const handleResumeProgress = () => {
    if (savedState) {
      setCurrentStep(savedState.currentStep)
      setUserData(savedState.userData)
      setShowResumeOption(false)
    }
  }

  const handleStartOver = () => {
    OnboardingStorage.clearState()
    setCurrentStep(0)
    setUserData({
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      currentLocation: "",
      branch: "",
      rank: "",
      mos: "",
      yearsOfService: "",
      separationDate: "",
      separationType: "",
      clearanceLevel: "",
      targetLocation: "",
      targetIndustry: "",
      salaryRange: "",
      jobType: "",
      educationGoals: "",
      careerTimeline: "",
      transitionPhase: "",
      completedTasks: [],
      urgentNeeds: [],
      timeUntilSeparation: "",
      topPriorities: [],
      timeCommitment: "",
      supportNeeds: [],
      communicationPreference: "",
      familyStatus: "",
    })
    setShowResumeOption(false)
    setSavedState(null)
  }

  const progress = ((currentStep + 1) / steps.length) * 100

  const handleNext = (stepData: any) => {
    const newUserData = { ...userData, ...stepData }
    setUserData(newUserData)

    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      // Complete onboarding
      OnboardingStorage.markCompleted()
      onComplete(newUserData)
    }
  }

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  // Show resume option if available
  if (showResumeOption) {
    return (
      <div className="min-h-screen bg-slate-900 p-4 flex items-center justify-center">
        <Card className="bg-slate-800 border-slate-700 max-w-md w-full">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertTriangle className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-white mb-2">Resume Your Setup?</h3>
              <p className="text-slate-400 mb-6">
                We found your previous progress from {new Date(savedState.timestamp).toLocaleDateString()}. You were on
                step {savedState.currentStep + 1} of {steps.length}.
              </p>

              <div className="space-y-3">
                <Button onClick={handleResumeProgress} className="w-full bg-blue-600 hover:bg-blue-700">
                  <Play className="w-4 h-4 mr-2" />
                  Resume Progress ({Math.round(((savedState.currentStep + 1) / steps.length) * 100)}% complete)
                </Button>

                <Button
                  onClick={handleStartOver}
                  variant="outline"
                  className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  <RotateCcw className="w-4 h-4 mr-2" />
                  Start Over
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const CurrentStepComponent = steps[currentStep].component

  return (
    <div className="min-h-screen bg-slate-900 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Welcome to MTCC</h1>
          <p className="text-slate-400">Let's set up your personalized transition command center</p>
        </div>

        {/* Progress */}
        <Card className="bg-slate-800 border-slate-700 mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center justify-between mb-4">
              <span className="text-slate-400 text-sm">
                Step {currentStep + 1} of {steps.length}
              </span>
              <div className="flex items-center space-x-4">
                <span className="text-white font-medium">{Math.round(progress)}% Complete</span>
                <Button
                  onClick={handleStartOver}
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-400 hover:bg-slate-700"
                >
                  <RotateCcw className="w-3 h-3 mr-1" />
                  Reset
                </Button>
              </div>
            </div>
            <Progress value={progress} className="h-2" />
            <div className="mt-2">
              <h3 className="text-white font-semibold">{steps[currentStep].title}</h3>
            </div>
          </CardContent>
        </Card>

        {/* Current Step */}
        <CurrentStepComponent data={userData} onNext={handleNext} onBack={currentStep > 0 ? handleBack : undefined} />
      </div>
    </div>
  )
}

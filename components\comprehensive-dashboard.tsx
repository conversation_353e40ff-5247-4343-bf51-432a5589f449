"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  FileText,
  MapPin,
  Target,
  TrendingUp,
  Users,
  AlertTriangle
} from "lucide-react"

interface ComprehensiveDashboardProps {
  user: any
}

export function ComprehensiveDashboard({ user }: ComprehensiveDashboardProps) {
  // Mock data - in a real app, this would come from your backend
  const dashboardData = {
    completionPercentage: user?.completionPercentage || 65,
    daysUntilSeparation: user?.daysUntilSeparation || 180,
    nextPriority: user?.nextPriority || "Complete VA Disability Claim",
    recentActivities: [
      { id: 1, action: "Completed TAP Workshop", date: "2 days ago", status: "completed" },
      { id: 2, action: "Updated Resume", date: "1 week ago", status: "completed" },
      { id: 3, action: "Applied to 3 Jobs", date: "3 days ago", status: "completed" },
      { id: 4, action: "Schedule VA Exam", date: "Pending", status: "pending" },
    ],
    upcomingTasks: [
      { id: 1, task: "VA Disability Exam", dueDate: "Jul 25, 2024", priority: "high" },
      { id: 2, task: "Job Interview - Tech Corp", dueDate: "Jul 28, 2024", priority: "high" },
      { id: 3, task: "Complete Security Clearance Transfer", dueDate: "Aug 5, 2024", priority: "medium" },
    ]
  }

  return (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Days Until Separation</p>
                <p className="text-2xl font-bold text-white">{dashboardData.daysUntilSeparation}</p>
              </div>
              <Calendar className="w-8 h-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Completion Progress</p>
                <p className="text-2xl font-bold text-white">{dashboardData.completionPercentage}%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Active Applications</p>
                <p className="text-2xl font-bold text-white">12</p>
              </div>
              <FileText className="w-8 h-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Network Connections</p>
                <p className="text-2xl font-bold text-white">47</p>
              </div>
              <Users className="w-8 h-8 text-orange-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Transition Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-slate-300">Overall Completion</span>
              <span className="text-white font-semibold">{dashboardData.completionPercentage}%</span>
            </div>
            <Progress value={dashboardData.completionPercentage} className="h-3" />
            <p className="text-slate-400 text-sm">
              Next Priority: {dashboardData.nextPriority}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity & Upcoming Tasks */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <CheckCircle className="w-5 h-5 mr-2" />
              Recent Activities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData.recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
                  <div>
                    <p className="text-white font-medium">{activity.action}</p>
                    <p className="text-slate-400 text-sm">{activity.date}</p>
                  </div>
                  <Badge
                    variant={activity.status === 'completed' ? 'default' : 'secondary'}
                    className={activity.status === 'completed' ? 'bg-green-600' : 'bg-yellow-600'}
                  >
                    {activity.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Tasks */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              Upcoming Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardData.upcomingTasks.map((task) => (
                <div key={task.id} className="flex items-center justify-between p-3 bg-slate-700/50 rounded-lg">
                  <div>
                    <p className="text-white font-medium">{task.task}</p>
                    <p className="text-slate-400 text-sm">{task.dueDate}</p>
                  </div>
                  <Badge
                    variant={task.priority === 'high' ? 'destructive' : 'secondary'}
                  >
                    {task.priority}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

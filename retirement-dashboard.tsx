"use client"

import { useState } from "react"
import { Sidebar } from "./components/sidebar"
import { AICoach } from "./components/ai-coach"
import { FamilyDashboard } from "./components/family-dashboard"
import { StateBenefits } from "./components/state-benefits"
import { PeerMentorship } from "./components/peer-mentorship"
import { VATracker } from "./components/va-tracker"
import { FinancialCalculator } from "./components/financial-calculator"
import { JobMatching } from "./components/job-matching"
import { MobileApp } from "./components/mobile-app"
import { ComprehensiveDashboard } from "./components/comprehensive-dashboard"
import { MissionControl } from "./components/mission-control"
import { RetirementTimelinePage } from "./components/retirement-timeline-page"

export default function RetirementDashboard({ user }: { user: any }) {
  const [activeSection, setActiveSection] = useState("dashboard")

  const renderContent = () => {
    switch (activeSection) {
      case "mission-control":
        return <MissionControl user={user} />
      case "ai-coach":
        return <AICoach user={user} />
      case "family-dashboard":
        return <FamilyDashboard user={user} />
      case "state-benefits":
        return <StateBenefits user={user} />
      case "peer-mentorship":
        return <PeerMentorship user={user} />
      case "va-tracker":
        return <VATracker user={user} />
      case "financial-calculator":
        return <FinancialCalculator user={user} />
      case "job-matching":
        return <JobMatching user={user} />
      case "mobile-app":
        return <MobileApp user={user} />
      case "timeline":
        return <RetirementTimelinePage user={user} />
      default:
        return <ComprehensiveDashboard user={user} />
    }
  }

  return (
    <div className="flex h-screen bg-slate-900">
      <Sidebar activeSection={activeSection} onSectionChange={setActiveSection} user={user} />

      <div className="flex-1 overflow-auto">
        {/* Personalized Welcome Banner */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-white font-semibold">
                Welcome back, {user.rank} {user.lastName}
              </h2>
              <p className="text-blue-100 text-sm">
                {user.daysUntilSeparation} days until separation • {user.completionPercentage}% complete
              </p>
            </div>
            <div className="text-right">
              <p className="text-white text-sm">Next Priority:</p>
              <p className="text-blue-100 text-sm">{user.nextPriority}</p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="p-6">{renderContent()}</div>
      </div>
    </div>
  )
}

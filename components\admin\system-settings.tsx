"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Settings, Save, RefreshCw, Bell, Mail, Shield, Globe, Users } from "lucide-react"

export function SystemSettings() {
  const [settings, setSettings] = useState({
    // General Settings
    siteName: "MTCC - Military Transition Command Center",
    siteDescription: "Comprehensive transition planning for military personnel",
    maintenanceMode: false,
    registrationEnabled: true,

    // Notification Settings
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    notificationFrequency: "daily",

    // Security Settings
    sessionTimeout: "24",
    passwordMinLength: "8",
    requireTwoFactor: false,
    allowGoogleAuth: true,

    // Feature Flags
    aiCoachEnabled: true,
    peerMentorshipEnabled: true,
    stateBenefitsEnabled: true,
    jobMatchingEnabled: true,
    familyDashboardEnabled: true,
    mobileAppEnabled: true,

    // API Settings
    apiRateLimit: "1000",
    apiTimeout: "30",
    cacheEnabled: true,
    cacheDuration: "3600",

    // Content Settings
    welcomeMessage: "Welcome to your Military Transition Command Center",
    supportEmail: "<EMAIL>",
    supportPhone: "1-800-MTCC-HELP",
    privacyPolicyUrl: "https://mtcc.mil/privacy",
    termsOfServiceUrl: "https://mtcc.mil/terms",
  })

  const handleSettingChange = (key: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [key]: value,
    }))
  }

  const handleSaveSettings = () => {
    // In production, this would save to your backend
    console.log("Saving settings:", settings)
  }

  const handleResetSettings = () => {
    // Reset to default values
    setSettings({
      siteName: "MTCC - Military Transition Command Center",
      siteDescription: "Comprehensive transition planning for military personnel",
      maintenanceMode: false,
      registrationEnabled: true,
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      notificationFrequency: "daily",
      sessionTimeout: "24",
      passwordMinLength: "8",
      requireTwoFactor: false,
      allowGoogleAuth: true,
      aiCoachEnabled: true,
      peerMentorshipEnabled: true,
      stateBenefitsEnabled: true,
      jobMatchingEnabled: true,
      familyDashboardEnabled: true,
      mobileAppEnabled: true,
      apiRateLimit: "1000",
      apiTimeout: "30",
      cacheEnabled: true,
      cacheDuration: "3600",
      welcomeMessage: "Welcome to your Military Transition Command Center",
      supportEmail: "<EMAIL>",
      supportPhone: "1-800-MTCC-HELP",
      privacyPolicyUrl: "https://mtcc.mil/privacy",
      termsOfServiceUrl: "https://mtcc.mil/terms",
    })
  }

  return (
    <div className="space-y-6">
      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6 bg-slate-800 border-slate-700">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="api">API</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Settings className="w-5 h-5 mr-2" />
                General Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="siteName" className="text-slate-300">
                    Site Name
                  </Label>
                  <Input
                    id="siteName"
                    value={settings.siteName}
                    onChange={(e) => handleSettingChange("siteName", e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
                <div>
                  <Label htmlFor="siteDescription" className="text-slate-300">
                    Site Description
                  </Label>
                  <Input
                    id="siteDescription"
                    value={settings.siteDescription}
                    onChange={(e) => handleSettingChange("siteDescription", e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-300">Maintenance Mode</Label>
                    <p className="text-slate-400 text-sm">Temporarily disable user access for maintenance</p>
                  </div>
                  <Switch
                    checked={settings.maintenanceMode}
                    onCheckedChange={(checked) => handleSettingChange("maintenanceMode", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-300">User Registration</Label>
                    <p className="text-slate-400 text-sm">Allow new users to create accounts</p>
                  </div>
                  <Switch
                    checked={settings.registrationEnabled}
                    onCheckedChange={(checked) => handleSettingChange("registrationEnabled", checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Bell className="w-5 h-5 mr-2" />
                Notification Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-300">Email Notifications</Label>
                    <p className="text-slate-400 text-sm">Send notifications via email</p>
                  </div>
                  <Switch
                    checked={settings.emailNotifications}
                    onCheckedChange={(checked) => handleSettingChange("emailNotifications", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-300">Push Notifications</Label>
                    <p className="text-slate-400 text-sm">Send browser push notifications</p>
                  </div>
                  <Switch
                    checked={settings.pushNotifications}
                    onCheckedChange={(checked) => handleSettingChange("pushNotifications", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-300">SMS Notifications</Label>
                    <p className="text-slate-400 text-sm">Send notifications via SMS</p>
                  </div>
                  <Switch
                    checked={settings.smsNotifications}
                    onCheckedChange={(checked) => handleSettingChange("smsNotifications", checked)}
                  />
                </div>
              </div>

              <div>
                <Label className="text-slate-300">Default Notification Frequency</Label>
                <Select
                  value={settings.notificationFrequency}
                  onValueChange={(value) => handleSettingChange("notificationFrequency", value)}
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="immediate">Immediate</SelectItem>
                    <SelectItem value="daily">Daily Digest</SelectItem>
                    <SelectItem value="weekly">Weekly Summary</SelectItem>
                    <SelectItem value="monthly">Monthly Report</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Shield className="w-5 h-5 mr-2" />
                Security Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="sessionTimeout" className="text-slate-300">
                    Session Timeout (hours)
                  </Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={settings.sessionTimeout}
                    onChange={(e) => handleSettingChange("sessionTimeout", e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
                <div>
                  <Label htmlFor="passwordMinLength" className="text-slate-300">
                    Minimum Password Length
                  </Label>
                  <Input
                    id="passwordMinLength"
                    type="number"
                    value={settings.passwordMinLength}
                    onChange={(e) => handleSettingChange("passwordMinLength", e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-300">Require Two-Factor Authentication</Label>
                    <p className="text-slate-400 text-sm">Force all users to enable 2FA</p>
                  </div>
                  <Switch
                    checked={settings.requireTwoFactor}
                    onCheckedChange={(checked) => handleSettingChange("requireTwoFactor", checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-300">Allow Google Authentication</Label>
                    <p className="text-slate-400 text-sm">Enable Google OAuth login</p>
                  </div>
                  <Switch
                    checked={settings.allowGoogleAuth}
                    onCheckedChange={(checked) => handleSettingChange("allowGoogleAuth", checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="features">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Feature Flags
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {[
                {
                  key: "aiCoachEnabled",
                  label: "AI Transition Coach",
                  description: "Enable AI-powered coaching features",
                },
                {
                  key: "peerMentorshipEnabled",
                  label: "Peer Mentorship Network",
                  description: "Enable peer-to-peer mentorship",
                },
                {
                  key: "stateBenefitsEnabled",
                  label: "State Benefits Calculator",
                  description: "Enable state-specific benefits comparison",
                },
                {
                  key: "jobMatchingEnabled",
                  label: "Job Matching Engine",
                  description: "Enable AI-powered job matching",
                },
                {
                  key: "familyDashboardEnabled",
                  label: "Family Dashboard",
                  description: "Enable family transition planning tools",
                },
                {
                  key: "mobileAppEnabled",
                  label: "Mobile App Features",
                  description: "Enable mobile app functionality",
                },
              ].map((feature) => (
                <div key={feature.key} className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-300">{feature.label}</Label>
                    <p className="text-slate-400 text-sm">{feature.description}</p>
                  </div>
                  <Switch
                    checked={settings[feature.key as keyof typeof settings] as boolean}
                    onCheckedChange={(checked) => handleSettingChange(feature.key, checked)}
                  />
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="api">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Globe className="w-5 h-5 mr-2" />
                API Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="apiRateLimit" className="text-slate-300">
                    Rate Limit (requests/hour)
                  </Label>
                  <Input
                    id="apiRateLimit"
                    type="number"
                    value={settings.apiRateLimit}
                    onChange={(e) => handleSettingChange("apiRateLimit", e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
                <div>
                  <Label htmlFor="apiTimeout" className="text-slate-300">
                    API Timeout (seconds)
                  </Label>
                  <Input
                    id="apiTimeout"
                    type="number"
                    value={settings.apiTimeout}
                    onChange={(e) => handleSettingChange("apiTimeout", e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-slate-300">Enable Caching</Label>
                    <p className="text-slate-400 text-sm">Cache API responses for better performance</p>
                  </div>
                  <Switch
                    checked={settings.cacheEnabled}
                    onCheckedChange={(checked) => handleSettingChange("cacheEnabled", checked)}
                  />
                </div>

                <div>
                  <Label htmlFor="cacheDuration" className="text-slate-300">
                    Cache Duration (seconds)
                  </Label>
                  <Input
                    id="cacheDuration"
                    type="number"
                    value={settings.cacheDuration}
                    onChange={(e) => handleSettingChange("cacheDuration", e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                    disabled={!settings.cacheEnabled}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Mail className="w-5 h-5 mr-2" />
                Content & Support Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <Label htmlFor="welcomeMessage" className="text-slate-300">
                  Welcome Message
                </Label>
                <Textarea
                  id="welcomeMessage"
                  value={settings.welcomeMessage}
                  onChange={(e) => handleSettingChange("welcomeMessage", e.target.value)}
                  className="bg-slate-700 border-slate-600 text-white"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="supportEmail" className="text-slate-300">
                    Support Email
                  </Label>
                  <Input
                    id="supportEmail"
                    type="email"
                    value={settings.supportEmail}
                    onChange={(e) => handleSettingChange("supportEmail", e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
                <div>
                  <Label htmlFor="supportPhone" className="text-slate-300">
                    Support Phone
                  </Label>
                  <Input
                    id="supportPhone"
                    value={settings.supportPhone}
                    onChange={(e) => handleSettingChange("supportPhone", e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="privacyPolicyUrl" className="text-slate-300">
                    Privacy Policy URL
                  </Label>
                  <Input
                    id="privacyPolicyUrl"
                    type="url"
                    value={settings.privacyPolicyUrl}
                    onChange={(e) => handleSettingChange("privacyPolicyUrl", e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
                <div>
                  <Label htmlFor="termsOfServiceUrl" className="text-slate-300">
                    Terms of Service URL
                  </Label>
                  <Input
                    id="termsOfServiceUrl"
                    type="url"
                    value={settings.termsOfServiceUrl}
                    onChange={(e) => handleSettingChange("termsOfServiceUrl", e.target.value)}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex items-center justify-between">
        <Button onClick={handleResetSettings} variant="outline" className="border-slate-600 text-slate-300">
          <RefreshCw className="w-4 h-4 mr-2" />
          Reset to Defaults
        </Button>
        <Button onClick={handleSaveSettings} className="bg-green-600 hover:bg-green-700">
          <Save className="w-4 h-4 mr-2" />
          Save All Settings
        </Button>
      </div>
    </div>
  )
}

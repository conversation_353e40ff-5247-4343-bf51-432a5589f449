"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Briefcase, MapPin, DollarSign, Star, Building, Clock, Search, Filter } from "lucide-react"

export function JobMatching() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedLocation, setSelectedLocation] = useState("")
  const [selectedIndustry, setSelectedIndustry] = useState("")

  const userProfile = {
    mos: "Surface Warfare Officer",
    clearance: "Secret",
    location: "San Diego, CA",
    salaryRange: "$80,000 - $120,000",
    skills: ["Leadership", "Project Management", "Operations", "Team Building", "Strategic Planning"],
  }

  const jobMatches = [
    {
      id: 1,
      title: "Operations Manager",
      company: "Lockheed Martin",
      location: "San Diego, CA",
      salary: "$95,000 - $115,000",
      matchScore: 95,
      clearanceRequired: "Secret",
      remote: false,
      postedDate: "2 days ago",
      description: "Lead operations team in defense contracting environment. Military experience preferred.",
      requirements: ["Leadership experience", "Project management", "Security clearance"],
      benefits: ["Health insurance", "401k matching", "Veteran preference"],
      type: "Full-time",
    },
    {
      id: 2,
      title: "Program Manager",
      company: "General Dynamics",
      location: "San Diego, CA",
      salary: "$100,000 - $130,000",
      matchScore: 92,
      clearanceRequired: "Secret",
      remote: "Hybrid",
      postedDate: "1 week ago",
      description: "Manage complex defense programs with cross-functional teams.",
      requirements: ["PMP certification preferred", "Military background", "Team leadership"],
      benefits: ["Comprehensive healthcare", "Tuition assistance", "Flexible schedule"],
      type: "Full-time",
    },
    {
      id: 3,
      title: "Project Coordinator",
      company: "SAIC",
      location: "San Diego, CA",
      salary: "$75,000 - $95,000",
      matchScore: 88,
      clearanceRequired: "Secret",
      remote: true,
      postedDate: "3 days ago",
      description: "Coordinate multiple projects in government contracting space.",
      requirements: ["Organizational skills", "Military experience", "Communication skills"],
      benefits: ["Remote work", "Professional development", "Veteran hiring initiative"],
      type: "Full-time",
    },
    {
      id: 4,
      title: "Business Operations Analyst",
      company: "Booz Allen Hamilton",
      location: "San Diego, CA",
      salary: "$85,000 - $105,000",
      matchScore: 85,
      clearanceRequired: "Secret",
      remote: "Hybrid",
      postedDate: "5 days ago",
      description: "Analyze business operations and provide strategic recommendations.",
      requirements: ["Analytical skills", "Process improvement", "Military background preferred"],
      benefits: ["Career advancement", "Training programs", "Work-life balance"],
      type: "Full-time",
    },
    {
      id: 5,
      title: "Supply Chain Manager",
      company: "Northrop Grumman",
      location: "San Diego, CA",
      salary: "$90,000 - $110,000",
      matchScore: 82,
      clearanceRequired: "Secret",
      remote: false,
      postedDate: "1 week ago",
      description: "Manage supply chain operations for defense manufacturing.",
      requirements: ["Supply chain experience", "Leadership", "Process optimization"],
      benefits: ["Stock options", "Health benefits", "Retirement planning"],
      type: "Full-time",
    },
  ]

  const skillsGap = [
    { skill: "PMP Certification", importance: "High", hasSkill: false, recommendation: "Consider getting certified" },
    { skill: "Agile/Scrum", importance: "Medium", hasSkill: false, recommendation: "Take online course" },
    { skill: "Data Analysis", importance: "Medium", hasSkill: false, recommendation: "Learn Excel/Tableau" },
    { skill: "Cybersecurity Basics", importance: "Low", hasSkill: false, recommendation: "Optional certification" },
  ]

  const careerPaths = [
    {
      title: "Operations Management Track",
      timeline: "2-5 years",
      positions: ["Operations Coordinator", "Operations Manager", "Director of Operations"],
      salaryProgression: "$75k → $95k → $130k+",
    },
    {
      title: "Program Management Track",
      timeline: "3-7 years",
      positions: ["Project Coordinator", "Program Manager", "Senior Program Manager"],
      salaryProgression: "$80k → $110k → $150k+",
    },
    {
      title: "Business Leadership Track",
      timeline: "5-10 years",
      positions: ["Business Analyst", "Operations Manager", "VP Operations"],
      salaryProgression: "$85k → $120k → $180k+",
    },
  ]

  const getMatchColor = (score: number) => {
    if (score >= 90) return "text-green-400"
    if (score >= 80) return "text-yellow-400"
    return "text-red-400"
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">Job Matching Engine</h1>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Briefcase className="w-4 h-4 mr-2" />
          Upload Resume
        </Button>
      </div>

      {/* User Profile Summary */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Your Profile</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <p className="text-slate-400 text-sm">MOS/Specialty</p>
              <p className="text-white font-semibold">{userProfile.mos}</p>
            </div>
            <div>
              <p className="text-slate-400 text-sm">Security Clearance</p>
              <p className="text-white font-semibold">{userProfile.clearance}</p>
            </div>
            <div>
              <p className="text-slate-400 text-sm">Preferred Location</p>
              <p className="text-white font-semibold">{userProfile.location}</p>
            </div>
            <div>
              <p className="text-slate-400 text-sm">Salary Range</p>
              <p className="text-white font-semibold">{userProfile.salaryRange}</p>
            </div>
          </div>
          <div className="mt-4">
            <p className="text-slate-400 text-sm mb-2">Key Skills</p>
            <div className="flex flex-wrap gap-2">
              {userProfile.skills.map((skill, index) => (
                <Badge key={index} variant="outline" className="border-blue-600 text-blue-400">
                  {skill}
                </Badge>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search and Filters */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <Input
                placeholder="Search jobs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <Select value={selectedLocation} onValueChange={setSelectedLocation}>
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder="Location" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="san-diego">San Diego, CA</SelectItem>
                <SelectItem value="virginia-beach">Virginia Beach, VA</SelectItem>
                <SelectItem value="norfolk">Norfolk, VA</SelectItem>
                <SelectItem value="remote">Remote</SelectItem>
              </SelectContent>
            </Select>
            <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
              <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder="Industry" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="defense">Defense</SelectItem>
                <SelectItem value="technology">Technology</SelectItem>
                <SelectItem value="logistics">Logistics</SelectItem>
                <SelectItem value="consulting">Consulting</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
              <Filter className="w-4 h-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Job Listings */}
        <div className="lg:col-span-2 space-y-4">
          <h2 className="text-xl font-semibold text-white">Recommended Jobs ({jobMatches.length})</h2>
          {jobMatches.map((job) => (
            <Card key={job.id} className="bg-slate-800 border-slate-700">
              <CardContent className="pt-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h3 className="text-white font-semibold text-lg">{job.title}</h3>
                      <Badge variant="outline" className={`border-green-600 ${getMatchColor(job.matchScore)}`}>
                        {job.matchScore}% match
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-slate-400 mb-2">
                      <span className="flex items-center">
                        <Building className="w-4 h-4 mr-1" />
                        {job.company}
                      </span>
                      <span className="flex items-center">
                        <MapPin className="w-4 h-4 mr-1" />
                        {job.location}
                      </span>
                      <span className="flex items-center">
                        <DollarSign className="w-4 h-4 mr-1" />
                        {job.salary}
                      </span>
                    </div>
                    <p className="text-slate-300 text-sm mb-3">{job.description}</p>
                  </div>
                  <div className="text-right">
                    <Badge
                      variant={job.remote === true ? "default" : job.remote === "Hybrid" ? "secondary" : "outline"}
                      className="mb-2"
                    >
                      {job.remote === true ? "Remote" : job.remote === "Hybrid" ? "Hybrid" : "On-site"}
                    </Badge>
                    <p className="text-slate-400 text-xs flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {job.postedDate}
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <h4 className="text-white font-medium text-sm mb-2">Requirements</h4>
                    <ul className="text-slate-300 text-xs space-y-1">
                      {job.requirements.map((req, index) => (
                        <li key={index}>• {req}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-white font-medium text-sm mb-2">Benefits</h4>
                    <ul className="text-slate-300 text-xs space-y-1">
                      {job.benefits.map((benefit, index) => (
                        <li key={index}>• {benefit}</li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                      {job.clearanceRequired} Clearance
                    </Badge>
                    <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                      {job.type}
                    </Badge>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
                      Save
                    </Button>
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      Apply Now
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Skills Gap Analysis */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Star className="w-5 h-5 mr-2" />
                Skills Gap Analysis
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {skillsGap.map((item, index) => (
                <div key={index} className="border border-slate-700 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="text-white font-medium text-sm">{item.skill}</h4>
                    <Badge
                      variant={
                        item.importance === "High"
                          ? "destructive"
                          : item.importance === "Medium"
                            ? "secondary"
                            : "outline"
                      }
                      className="text-xs"
                    >
                      {item.importance}
                    </Badge>
                  </div>
                  <p className="text-slate-400 text-xs">{item.recommendation}</p>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Career Paths */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Career Progression Paths</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {careerPaths.map((path, index) => (
                <div key={index} className="border border-slate-700 rounded-lg p-3">
                  <h4 className="text-white font-medium text-sm mb-1">{path.title}</h4>
                  <p className="text-slate-400 text-xs mb-2">{path.timeline}</p>
                  <div className="space-y-1">
                    {path.positions.map((position, idx) => (
                      <div key={idx} className="text-slate-300 text-xs">
                        {idx + 1}. {position}
                      </div>
                    ))}
                  </div>
                  <p className="text-green-400 text-xs mt-2 font-medium">{path.salaryProgression}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

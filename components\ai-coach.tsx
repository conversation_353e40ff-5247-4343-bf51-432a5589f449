"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Bot,
  Send,
  User,
  Lightbulb,
  AlertCircle,
  CheckCircle,
  FileText,
  DollarSign,
  Briefcase,
  Heart,
} from "lucide-react"

export function AICoach() {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: "bot",
      content:
        "Hello! I'm your AI Transition Coach. I'm here to help guide you through your military retirement process. What would you like to know about today?",
      timestamp: "10:30 AM",
    },
    {
      id: 2,
      type: "user",
      content: "I'm worried about my VA claim timeline. When should I start?",
      timestamp: "10:32 AM",
    },
    {
      id: 3,
      type: "bot",
      content:
        "Great question! Based on your separation date, I recommend starting your VA claim 180 days before retirement using the Benefits Delivery at Discharge (BDD) program. This ensures you receive your rating decision within 30-45 days after separation. Would you like me to walk you through the process?",
      timestamp: "10:33 AM",
    },
  ])

  const [newMessage, setNewMessage] = useState("")

  const quickActions = [
    { text: "Help with VA claims", icon: FileText },
    { text: "Financial planning advice", icon: DollarSign },
    { text: "Job search strategies", icon: Briefcase },
    { text: "Family transition support", icon: Heart },
  ]

  const coachingTopics = [
    { title: "VA Claims Preparation", status: "in-progress", priority: "high" },
    { title: "Resume Optimization", status: "completed", priority: "medium" },
    { title: "Interview Preparation", status: "not-started", priority: "medium" },
    { title: "Financial Planning", status: "in-progress", priority: "high" },
  ]

  const handleSendMessage = () => {
    if (!newMessage.trim()) return

    const userMessage = {
      id: messages.length + 1,
      type: "user" as const,
      content: newMessage,
      timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
    }

    setMessages([...messages, userMessage])
    setNewMessage("")

    // Simulate AI response
    setTimeout(() => {
      const botResponse = {
        id: messages.length + 2,
        type: "bot" as const,
        content:
          "I understand your concern. Let me provide you with personalized guidance based on your profile and current progress...",
        timestamp: new Date().toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" }),
      }
      setMessages((prev) => [...prev, botResponse])
    }, 1000)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">AI Transition Coach</h1>
        <Badge variant="outline" className="border-green-500 text-green-400">
          <Bot className="w-4 h-4 mr-1" />
          Online
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Chat Interface */}
        <div className="lg:col-span-2">
          <Card className="bg-slate-800 border-slate-700 h-[600px] flex flex-col">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Bot className="w-5 h-5 mr-2" />
                Chat with Your AI Coach
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              {/* Messages */}
              <div className="flex-1 overflow-y-auto space-y-4 mb-4">
                {messages.map((message) => (
                  <div key={message.id} className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}>
                    <div
                      className={`flex items-start space-x-2 max-w-[80%] ${message.type === "user" ? "flex-row-reverse space-x-reverse" : ""}`}
                    >
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center ${message.type === "user" ? "bg-blue-600" : "bg-green-600"}`}
                      >
                        {message.type === "user" ? (
                          <User className="w-4 h-4 text-white" />
                        ) : (
                          <Bot className="w-4 h-4 text-white" />
                        )}
                      </div>
                      <div
                        className={`rounded-lg p-3 ${message.type === "user" ? "bg-blue-600 text-white" : "bg-slate-700 text-slate-200"}`}
                      >
                        <p className="text-sm">{message.content}</p>
                        <span className="text-xs opacity-70 mt-1 block">{message.timestamp}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Quick Actions */}
              <div className="mb-4">
                <p className="text-slate-400 text-sm mb-2">Quick actions:</p>
                <div className="flex flex-wrap gap-2">
                  {quickActions.map((action, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      className="border-slate-600 text-slate-300 hover:bg-slate-700"
                      onClick={() => setNewMessage(action.text)}
                    >
                      {action.icon({ className: "w-4 h-4 mr-1" })}
                      {action.text}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Input */}
              <div className="flex space-x-2">
                <Input
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Ask me anything about your transition..."
                  className="bg-slate-700 border-slate-600 text-white"
                  onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                />
                <Button onClick={handleSendMessage} className="bg-blue-600 hover:bg-blue-700">
                  <Send className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Coaching Progress */}
        <div className="space-y-6">
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Lightbulb className="w-5 h-5 mr-2" />
                Coaching Topics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {coachingTopics.map((topic, index) => (
                <div key={index} className="border border-slate-700 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-white text-sm font-medium">{topic.title}</h4>
                    {topic.status === "completed" && <CheckCircle className="w-4 h-4 text-green-400" />}
                    {topic.status === "in-progress" && <AlertCircle className="w-4 h-4 text-yellow-400" />}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant={
                        topic.status === "completed"
                          ? "default"
                          : topic.status === "in-progress"
                            ? "secondary"
                            : "outline"
                      }
                      className="text-xs"
                    >
                      {topic.status.replace("-", " ")}
                    </Badge>
                    <Badge variant={topic.priority === "high" ? "destructive" : "outline"} className="text-xs">
                      {topic.priority} priority
                    </Badge>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">AI Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-3">
                  <p className="text-blue-300 text-sm">
                    💡 Based on your MOS and location preferences, I found 12 relevant job opportunities that match your
                    skills.
                  </p>
                </div>
                <div className="bg-green-900/20 border border-green-700/30 rounded-lg p-3">
                  <p className="text-green-300 text-sm">
                    ✅ Your VA claim preparation is 75% complete. Great progress!
                  </p>
                </div>
                <div className="bg-yellow-900/20 border border-yellow-700/30 rounded-lg p-3">
                  <p className="text-yellow-300 text-sm">
                    ⚠️ Consider scheduling your SHPE exam soon - you're approaching the optimal timeframe.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Users, Settings, Palette, Database, Activity, Shield, LogOut, Plus, Download, Upload } from "lucide-react"
import { UserProfileManager } from "./user-profile-manager"
import { ThemeManager } from "./theme-manager"
import { SystemSettings } from "./system-settings"
import { AdminStorage } from "@/lib/storage"

interface AdminDashboardProps {
  admin: any
  onLogout: () => void
}

export function AdminDashboard({ admin, onLogout }: AdminDashboardProps) {
  const [activeTab, setActiveTab] = useState("users")

  // Mock data - in production this would come from your backend
  const systemStats = {
    totalUsers: 1247,
    activeUsers: 892,
    newUsersToday: 23,
    completedOnboarding: 1156,
    pendingSupport: 12,
    systemHealth: "Excellent",
  }

  const recentActivity = [
    { id: 1, action: "New user registration", user: "<PERSON>", timestamp: "2 minutes ago" },
    { id: 2, action: "Profile updated", user: "Sarah Johnson", timestamp: "5 minutes ago" },
    { id: 3, action: "VA claim submitted", user: "Mike Rodriguez", timestamp: "12 minutes ago" },
    { id: 4, action: "Job application", user: "Lisa Chen", timestamp: "18 minutes ago" },
    { id: 5, action: "Mentor session completed", user: "David Thompson", timestamp: "25 minutes ago" },
  ]

  const handleLogout = () => {
    AdminStorage.clearAdminSession()
    onLogout()
  }

  return (
    <div className="min-h-screen bg-slate-900">
      {/* Admin Header */}
      <div className="bg-slate-800 border-b border-slate-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center">
              <Shield className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">MTCC Administration</h1>
              <p className="text-slate-400 text-sm">Logged in as {admin.username}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Badge variant="outline" className="border-green-600 text-green-400">
              System Healthy
            </Badge>
            <Button onClick={handleLogout} variant="outline" className="border-slate-600 text-slate-300">
              <LogOut className="w-4 h-4 mr-2" />
              Logout
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* System Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">Total Users</p>
                  <p className="text-2xl font-bold text-white">{systemStats.totalUsers.toLocaleString()}</p>
                </div>
                <Users className="w-8 h-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">Active Users</p>
                  <p className="text-2xl font-bold text-white">{systemStats.activeUsers.toLocaleString()}</p>
                </div>
                <Activity className="w-8 h-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">New Today</p>
                  <p className="text-2xl font-bold text-white">{systemStats.newUsersToday}</p>
                </div>
                <Plus className="w-8 h-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-slate-800 border-slate-700">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-slate-400 text-sm">Onboarding Complete</p>
                  <p className="text-2xl font-bold text-white">
                    {Math.round((systemStats.completedOnboarding / systemStats.totalUsers) * 100)}%
                  </p>
                </div>
                <Database className="w-8 h-8 text-orange-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Admin Interface */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 bg-slate-800 border-slate-700">
            <TabsTrigger value="users" className="flex items-center space-x-2">
              <Users className="w-4 h-4" />
              <span>Users</span>
            </TabsTrigger>
            <TabsTrigger value="themes" className="flex items-center space-x-2">
              <Palette className="w-4 h-4" />
              <span>Themes</span>
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center space-x-2">
              <Settings className="w-4 h-4" />
              <span>Settings</span>
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center space-x-2">
              <Activity className="w-4 h-4" />
              <span>Activity</span>
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center space-x-2">
              <Database className="w-4 h-4" />
              <span>System</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="users">
            <UserProfileManager />
          </TabsContent>

          <TabsContent value="themes">
            <ThemeManager />
          </TabsContent>

          <TabsContent value="settings">
            <SystemSettings />
          </TabsContent>

          <TabsContent value="activity">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div
                      key={activity.id}
                      className="flex items-center justify-between p-3 border border-slate-700 rounded-lg"
                    >
                      <div>
                        <p className="text-white text-sm">{activity.action}</p>
                        <p className="text-slate-400 text-xs">User: {activity.user}</p>
                      </div>
                      <span className="text-slate-400 text-xs">{activity.timestamp}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="system">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">System Health</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-slate-400">Database Status</span>
                      <Badge className="bg-green-600">Online</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">API Status</span>
                      <Badge className="bg-green-600">Healthy</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">Storage Usage</span>
                      <span className="text-white">67%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-slate-400">Active Sessions</span>
                      <span className="text-white">892</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white">Data Management</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700">
                    <Download className="w-4 h-4 mr-2" />
                    Export User Data
                  </Button>
                  <Button className="w-full bg-purple-600 hover:bg-purple-700">
                    <Upload className="w-4 h-4 mr-2" />
                    Import Configuration
                  </Button>
                  <Button variant="outline" className="w-full border-slate-600 text-slate-300">
                    <Database className="w-4 h-4 mr-2" />
                    Database Backup
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

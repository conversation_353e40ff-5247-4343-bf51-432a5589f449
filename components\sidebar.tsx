"use client"

import {
  Home,
  Target,
  Bot,
  Users,
  MapPin,
  Heart,
  FileText,
  Calculator,
  Briefcase,
  Smartphone,
  Calendar,
  Shield,
  LogOut
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

interface SidebarProps {
  activeSection: string
  onSectionChange: (section: string) => void
  user: any
}

const navigationItems = [
  {
    id: "dashboard",
    label: "Dashboard",
    icon: Home,
    description: "Your command center overview",
  },
  {
    id: "mission-control",
    label: "Mission Control",
    icon: Target,
    description: "Strategic transition overview",
  },
  {
    id: "ai-coach",
    label: "AI Coach",
    icon: Bo<PERSON>,
    description: "Personalized guidance",
  },
  {
    id: "family-dashboard",
    label: "Family Hub",
    icon: Heart,
    description: "Family transition support",
  },
  {
    id: "state-benefits",
    label: "State Benefits",
    icon: MapPin,
    description: "Location-based benefits",
  },
  {
    id: "peer-mentorship",
    label: "Mentorship",
    icon: Users,
    description: "Connect with veterans",
  },
  {
    id: "va-tracker",
    label: "VA Tracker",
    icon: FileText,
    description: "Benefits and claims",
  },
  {
    id: "financial-calculator",
    label: "Financial Tools",
    icon: Calculator,
    description: "Retirement planning",
  },
  {
    id: "job-matching",
    label: "Job Matching",
    icon: Briefcase,
    description: "Career opportunities",
  },
  {
    id: "timeline",
    label: "Timeline",
    icon: Calendar,
    description: "Transition roadmap",
  },
  {
    id: "mobile-app",
    label: "Mobile App",
    icon: Smartphone,
    description: "Download our app",
  },
]

export function Sidebar({ activeSection, onSectionChange, user }: SidebarProps) {
  const handleLogout = () => {
    // This would typically call a logout function passed as prop
    window.location.reload()
  }

  return (
    <div className="w-64 bg-slate-800 border-r border-slate-700 flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-slate-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
            <Shield className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-white font-semibold">MTCC</h2>
            <p className="text-slate-400 text-xs">Command Center</p>
          </div>
        </div>
      </div>

      {/* User Info */}
      <div className="p-4 border-b border-slate-700">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-slate-600 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-semibold">
              {user?.firstName?.[0] || 'U'}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-white text-sm font-medium truncate">
              {user?.rank} {user?.lastName || 'User'}
            </p>
            <p className="text-slate-400 text-xs truncate">
              {user?.branch || 'Service Member'}
            </p>
          </div>
        </div>
        {user?.daysUntilSeparation && (
          <div className="mt-3">
            <Badge variant="outline" className="border-blue-600 text-blue-400 text-xs">
              {user.daysUntilSeparation} days to go
            </Badge>
          </div>
        )}
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-y-auto p-4">
        <nav className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon
            const isActive = activeSection === item.id

            return (
              <button
                key={item.id}
                type="button"
                onClick={() => onSectionChange(item.id)}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  isActive
                    ? "bg-blue-600 text-white"
                    : "text-slate-300 hover:bg-slate-700 hover:text-white"
                }`}
              >
                <Icon className="w-4 h-4 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{item.label}</p>
                  <p className={`text-xs truncate ${
                    isActive ? "text-blue-100" : "text-slate-400"
                  }`}>
                    {item.description}
                  </p>
                </div>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-slate-700">
        <Button
          onClick={handleLogout}
          variant="outline"
          size="sm"
          className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          <LogOut className="w-4 h-4 mr-2" />
          Sign Out
        </Button>
      </div>
    </div>
  )
}

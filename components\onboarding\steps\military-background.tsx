"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Shield, Calendar } from "lucide-react"

interface MilitaryBackgroundProps {
  data: any
  onNext: (data: any) => void
  onBack?: () => void
}

export function MilitaryBackground({ data, onNext, onBack }: MilitaryBackgroundProps) {
  const [formData, setFormData] = useState({
    branch: data.branch || "",
    rank: data.rank || "",
    mos: data.mos || "",
    yearsOfService: data.yearsOfService || "",
    separationDate: data.separationDate || "",
    separationType: data.separationType || "",
    clearanceLevel: data.clearanceLevel || "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onNext(formData)
  }

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <Shield className="w-5 h-5 mr-2" />
          Military Background
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="branch" className="text-slate-300">
                Branch of Service *
              </Label>
              <Select value={formData.branch} onValueChange={(value) => setFormData({ ...formData, branch: value })}>
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="Select branch" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  <SelectItem value="army">Army</SelectItem>
                  <SelectItem value="navy">Navy</SelectItem>
                  <SelectItem value="air-force">Air Force</SelectItem>
                  <SelectItem value="marines">Marines</SelectItem>
                  <SelectItem value="coast-guard">Coast Guard</SelectItem>
                  <SelectItem value="space-force">Space Force</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="rank" className="text-slate-300">
                Current/Final Rank *
              </Label>
              <Input
                id="rank"
                value={formData.rank}
                onChange={(e) => setFormData({ ...formData, rank: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="e.g., Captain, E-7, etc."
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="mos" className="text-slate-300">
              MOS/Rating/AFSC *
            </Label>
            <Input
              id="mos"
              value={formData.mos}
              onChange={(e) => setFormData({ ...formData, mos: e.target.value })}
              className="bg-slate-700 border-slate-600 text-white"
              placeholder="e.g., 11B, IT, 3D1X2, etc."
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="yearsOfService" className="text-slate-300">
                Years of Service *
              </Label>
              <Input
                id="yearsOfService"
                type="number"
                value={formData.yearsOfService}
                onChange={(e) => setFormData({ ...formData, yearsOfService: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="20"
                required
              />
            </div>

            <div>
              <Label htmlFor="separationDate" className="text-slate-300">
                Separation/Retirement Date *
              </Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <Input
                  id="separationDate"
                  type="date"
                  value={formData.separationDate}
                  onChange={(e) => setFormData({ ...formData, separationDate: e.target.value })}
                  className="pl-10 bg-slate-700 border-slate-600 text-white"
                  required
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="separationType" className="text-slate-300">
                Separation Type *
              </Label>
              <Select
                value={formData.separationType}
                onValueChange={(value) => setFormData({ ...formData, separationType: value })}
              >
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  <SelectItem value="retirement">Retirement (20+ years)</SelectItem>
                  <SelectItem value="ets">ETS/End of Contract</SelectItem>
                  <SelectItem value="medical">Medical Separation</SelectItem>
                  <SelectItem value="early-retirement">Early Retirement</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="clearanceLevel" className="text-slate-300">
                Security Clearance
              </Label>
              <Select
                value={formData.clearanceLevel}
                onValueChange={(value) => setFormData({ ...formData, clearanceLevel: value })}
              >
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="Select clearance" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  <SelectItem value="none">None</SelectItem>
                  <SelectItem value="confidential">Confidential</SelectItem>
                  <SelectItem value="secret">Secret</SelectItem>
                  <SelectItem value="top-secret">Top Secret</SelectItem>
                  <SelectItem value="ts-sci">TS/SCI</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-between pt-6">
            {onBack && (
              <Button type="button" variant="outline" onClick={onBack} className="border-slate-600 text-slate-300">
                Back
              </Button>
            )}
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700 ml-auto">
              Continue
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

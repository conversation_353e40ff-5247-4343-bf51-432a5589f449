"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Heart, Users, GraduationCap, Calendar, AlertTriangle, CheckCircle } from "lucide-react"

export function FamilyDashboard() {
  const familyMembers = [
    {
      name: "<PERSON>",
      relationship: "Spouse",
      status: "Active",
      tasks: 3,
      completedTasks: 2,
      concerns: ["Healthcare transition", "Job search support"],
    },
    {
      name: "<PERSON>",
      relationship: "Son",
      age: 16,
      status: "Student",
      tasks: 2,
      completedTasks: 1,
      concerns: ["School transfer", "College planning"],
    },
    {
      name: "<PERSON>",
      relationship: "Daughter",
      age: 12,
      status: "Student",
      tasks: 1,
      completedTasks: 1,
      concerns: ["New school adjustment"],
    },
  ]

  const familyTasks = [
    {
      title: "Update DEERS Information",
      assignedTo: "Spouse",
      dueDate: "Jul 15, 2024",
      status: "completed",
      priority: "high",
    },
    {
      title: "Research School Districts",
      assignedTo: "Both Parents",
      dueDate: "Jul 20, 2024",
      status: "in-progress",
      priority: "high",
    },
    {
      title: "Schedule Family Counseling",
      assignedTo: "Service Member",
      dueDate: "Jul 25, 2024",
      status: "not-started",
      priority: "medium",
    },
    {
      title: "Plan Farewell Activities",
      assignedTo: "Family",
      dueDate: "Aug 1, 2024",
      status: "not-started",
      priority: "low",
    },
  ]

  const resources = [
    {
      title: "Military Family Life Counselors",
      description: "Free counseling services for military families",
      category: "Support",
    },
    {
      title: "School Liaison Officer",
      description: "Help with school transfers and educational planning",
      category: "Education",
    },
    {
      title: "Exceptional Family Member Program",
      description: "Support for families with special needs",
      category: "Special Needs",
    },
    {
      title: "Military Child Education Coalition",
      description: "Resources for military children's education",
      category: "Education",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">Family Dashboard</h1>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Users className="w-4 h-4 mr-2" />
          Add Family Member
        </Button>
      </div>

      {/* Family Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {familyMembers.map((member, index) => (
          <Card key={index} className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center justify-between">
                <span className="flex items-center">
                  <Heart className="w-5 h-5 mr-2 text-red-400" />
                  {member.name}
                </span>
                <Badge variant="outline" className="border-slate-600 text-slate-300">
                  {member.relationship}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {member.age && <p className="text-slate-400 text-sm">Age: {member.age}</p>}
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-slate-400">Tasks Progress</span>
                    <span className="text-white">
                      {member.completedTasks}/{member.tasks}
                    </span>
                  </div>
                  <Progress value={(member.completedTasks / member.tasks) * 100} className="h-2" />
                </div>
                <div>
                  <p className="text-slate-400 text-sm mb-2">Current Concerns:</p>
                  <div className="space-y-1">
                    {member.concerns.map((concern, idx) => (
                      <Badge key={idx} variant="outline" className="text-xs border-yellow-600 text-yellow-400 mr-1">
                        {concern}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Family Tasks */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <CheckCircle className="w-5 h-5 mr-2" />
              Family Tasks
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {familyTasks.map((task, index) => (
              <div key={index} className="border border-slate-700 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="text-white font-medium text-sm">{task.title}</h4>
                  <div className="flex items-center space-x-2">
                    {task.priority === "high" && <AlertTriangle className="w-4 h-4 text-red-400" />}
                    {task.status === "completed" && <CheckCircle className="w-4 h-4 text-green-400" />}
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                      {task.assignedTo}
                    </Badge>
                    <Badge
                      variant={
                        task.status === "completed"
                          ? "default"
                          : task.status === "in-progress"
                            ? "secondary"
                            : "outline"
                      }
                      className="text-xs"
                    >
                      {task.status.replace("-", " ")}
                    </Badge>
                  </div>
                  <span className="text-slate-400 text-xs">Due: {task.dueDate}</span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Family Resources */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white flex items-center">
              <GraduationCap className="w-5 h-5 mr-2" />
              Family Resources
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {resources.map((resource, index) => (
              <div key={index} className="border border-slate-700 rounded-lg p-4">
                <div className="flex items-start justify-between mb-2">
                  <h4 className="text-white font-medium text-sm">{resource.title}</h4>
                  <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                    {resource.category}
                  </Badge>
                </div>
                <p className="text-slate-400 text-xs">{resource.description}</p>
                <Button variant="outline" size="sm" className="mt-2 border-slate-600 text-slate-300 hover:bg-slate-700">
                  Learn More
                </Button>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Family Calendar */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Calendar className="w-5 h-5 mr-2" />
            Family Calendar & Events
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-blue-900/20 border border-blue-700/30 rounded-lg p-3">
              <h4 className="text-blue-300 font-medium text-sm mb-1">School Visit</h4>
              <p className="text-blue-200 text-xs">Jul 18, 2024 - 2:00 PM</p>
              <p className="text-blue-400 text-xs mt-1">Michael & Emma</p>
            </div>
            <div className="bg-green-900/20 border border-green-700/30 rounded-lg p-3">
              <h4 className="text-green-300 font-medium text-sm mb-1">Family Counseling</h4>
              <p className="text-green-200 text-xs">Jul 22, 2024 - 10:00 AM</p>
              <p className="text-green-400 text-xs mt-1">Whole Family</p>
            </div>
            <div className="bg-purple-900/20 border border-purple-700/30 rounded-lg p-3">
              <h4 className="text-purple-300 font-medium text-sm mb-1">House Hunting</h4>
              <p className="text-purple-200 text-xs">Jul 25, 2024 - 9:00 AM</p>
              <p className="text-purple-400 text-xs mt-1">Parents</p>
            </div>
            <div className="bg-yellow-900/20 border border-yellow-700/30 rounded-lg p-3">
              <h4 className="text-yellow-300 font-medium text-sm mb-1">Farewell Party</h4>
              <p className="text-yellow-200 text-xs">Aug 5, 2024 - 6:00 PM</p>
              <p className="text-yellow-400 text-xs mt-1">Whole Family</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { UserCheck, MessageCircle, Star, MapPin, Briefcase, Calendar, Search } from "lucide-react"

export function PeerMentorship() {
  const [searchTerm, setSearchTerm] = useState("")

  const mentors = [
    {
      id: 1,
      name: "<PERSON>",
      rank: "Former Captain",
      branch: "Navy",
      mos: "Surface Warfare Officer",
      location: "San Diego, CA",
      industry: "Technology",
      company: "Microsoft",
      yearsRetired: 2,
      rating: 4.9,
      specialties: ["Tech Transition", "Leadership", "Resume Writing"],
      bio: "Transitioned from Navy SWO to Program Manager at Microsoft. Passionate about helping fellow veterans navigate tech careers.",
      availability: "Available",
      sessions: 47,
    },
    {
      id: 2,
      name: "<PERSON>",
      rank: "Former Master Chief",
      branch: "Navy",
      mos: "Information Systems Technician",
      location: "Austin, TX",
      industry: "Cybersecurity",
      company: "CyberArk",
      yearsRetired: 3,
      rating: 4.8,
      specialties: ["Cybersecurity", "Certifications", "Networking"],
      bio: "20-year Navy IT veteran now leading cybersecurity teams. Expert in security clearance transitions.",
      availability: "Busy",
      sessions: 62,
    },
    {
      id: 3,
      name: "Jennifer Chen",
      rank: "Former Lieutenant Commander",
      branch: "Navy",
      mos: "Supply Corps Officer",
      location: "Virginia Beach, VA",
      industry: "Logistics",
      company: "Amazon",
      yearsRetired: 1,
      rating: 4.7,
      specialties: ["Supply Chain", "Project Management", "PMP Certification"],
      bio: "Recently transitioned Supply Corps Officer now managing logistics operations at Amazon.",
      availability: "Available",
      sessions: 23,
    },
    {
      id: 4,
      name: "David Thompson",
      rank: "Former Senior Chief",
      branch: "Navy",
      mos: "Aviation Maintenance",
      location: "Phoenix, AZ",
      industry: "Aerospace",
      company: "Boeing",
      yearsRetired: 4,
      rating: 4.9,
      specialties: ["Aviation", "Quality Assurance", "Team Leadership"],
      bio: "Veteran aviation maintainer now leading quality teams at Boeing. Specializes in technical transitions.",
      availability: "Available",
      sessions: 89,
    },
  ]

  const upcomingSessions = [
    {
      mentor: "Sarah Mitchell",
      date: "Jul 18, 2024",
      time: "2:00 PM EST",
      topic: "Resume Review",
      type: "Video Call",
    },
    {
      mentor: "Marcus Rodriguez",
      date: "Jul 22, 2024",
      time: "10:00 AM CST",
      topic: "Cybersecurity Career Path",
      type: "Phone Call",
    },
  ]

  const mentorshipPrograms = [
    {
      title: "Tech Transition Bootcamp",
      duration: "8 weeks",
      participants: 12,
      nextStart: "Aug 1, 2024",
      description: "Intensive program for veterans transitioning to technology careers",
    },
    {
      title: "Leadership in Civilian Sector",
      duration: "6 weeks",
      participants: 8,
      nextStart: "Jul 25, 2024",
      description: "Translating military leadership skills to corporate environments",
    },
    {
      title: "Entrepreneurship for Veterans",
      duration: "10 weeks",
      participants: 15,
      nextStart: "Aug 15, 2024",
      description: "Starting your own business after military service",
    },
  ]

  const filteredMentors = mentors.filter(
    (mentor) =>
      mentor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.industry.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mentor.specialties.some((specialty) => specialty.toLowerCase().includes(searchTerm.toLowerCase())),
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-white">Peer Mentorship Network</h1>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <UserCheck className="w-4 h-4 mr-2" />
          Become a Mentor
        </Button>
      </div>

      {/* Search and Filter */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <Input
                placeholder="Search by name, industry, or specialty..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
              Filter
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Mentors List */}
        <div className="lg:col-span-2 space-y-4">
          <h2 className="text-xl font-semibold text-white">Available Mentors</h2>
          {filteredMentors.map((mentor) => (
            <Card key={mentor.id} className="bg-slate-800 border-slate-700">
              <CardContent className="pt-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold">
                        {mentor.name
                          .split(" ")
                          .map((n) => n[0])
                          .join("")}
                      </span>
                    </div>
                    <div>
                      <h3 className="text-white font-semibold">{mentor.name}</h3>
                      <p className="text-slate-400 text-sm">
                        {mentor.rank} • {mentor.branch}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <MapPin className="w-3 h-3 text-slate-400" />
                        <span className="text-slate-400 text-xs">{mentor.location}</span>
                        <Briefcase className="w-3 h-3 text-slate-400 ml-2" />
                        <span className="text-slate-400 text-xs">{mentor.company}</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-white text-sm">{mentor.rating}</span>
                    </div>
                    <p className="text-slate-400 text-xs">{mentor.sessions} sessions</p>
                    <Badge variant={mentor.availability === "Available" ? "default" : "secondary"} className="mt-1">
                      {mentor.availability}
                    </Badge>
                  </div>
                </div>

                <p className="text-slate-300 text-sm mb-3">{mentor.bio}</p>

                <div className="flex flex-wrap gap-2 mb-4">
                  {mentor.specialties.map((specialty, index) => (
                    <Badge key={index} variant="outline" className="border-blue-600 text-blue-400">
                      {specialty}
                    </Badge>
                  ))}
                </div>

                <div className="flex space-x-2">
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                    <MessageCircle className="w-4 h-4 mr-1" />
                    Connect
                  </Button>
                  <Button size="sm" variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
                    View Profile
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Upcoming Sessions */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <Calendar className="w-5 h-5 mr-2" />
                Upcoming Sessions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {upcomingSessions.map((session, index) => (
                <div key={index} className="border border-slate-700 rounded-lg p-3">
                  <h4 className="text-white font-medium text-sm">{session.topic}</h4>
                  <p className="text-slate-400 text-xs">with {session.mentor}</p>
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-slate-300 text-xs">
                      {session.date} at {session.time}
                    </span>
                    <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                      {session.type}
                    </Badge>
                  </div>
                </div>
              ))}
              <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700">
                Schedule New Session
              </Button>
            </CardContent>
          </Card>

          {/* Mentorship Programs */}
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Group Programs</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {mentorshipPrograms.map((program, index) => (
                <div key={index} className="border border-slate-700 rounded-lg p-3">
                  <h4 className="text-white font-medium text-sm mb-1">{program.title}</h4>
                  <p className="text-slate-400 text-xs mb-2">{program.description}</p>
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-slate-300">{program.duration}</span>
                    <span className="text-slate-300">{program.participants} spots</span>
                  </div>
                  <p className="text-blue-400 text-xs mt-1">Starts: {program.nextStart}</p>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

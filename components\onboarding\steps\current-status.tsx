"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Activity, CheckSquare, AlertTriangle, Info } from "lucide-react"

interface CurrentStatusProps {
  data: any
  onNext: (data: any) => void
  onBack?: () => void
}

export function CurrentStatus({ data, onNext, onBack }: CurrentStatusProps) {
  const [formData, setFormData] = useState({
    transitionPhase: data.transitionPhase || "",
    completedTasks: data.completedTasks || [],
    urgentNeeds: data.urgentNeeds || [],
    timeUntilSeparation: data.timeUntilSeparation || "",
  })

  const availableTasks = [
    { id: "tap-workshop", label: "Attended TAP Workshop", category: "Education" },
    { id: "resume-updated", label: "Updated Resume", category: "Career" },
    { id: "va-claim-started", label: "Started VA Disability Claim", category: "Benefits" },
    { id: "clearance-briefed", label: "Completed Security Clearance Debriefing", category: "Security" },
    { id: "medical-records", label: "Obtained Medical Records", category: "Medical" },
    { id: "dd214-requested", label: "Requested DD-214", category: "Documentation" },
    { id: "job-search-started", label: "Started Job Search", category: "Career" },
    { id: "linkedin-profile", label: "Created/Updated LinkedIn", category: "Career" },
    { id: "networking-started", label: "Started Professional Networking", category: "Career" },
    { id: "education-benefits", label: "Researched Education Benefits", category: "Education" },
  ]

  const urgentNeedOptions = [
    { id: "va-claim-help", label: "VA Disability Claim Assistance", icon: "🏥" },
    { id: "job-search-help", label: "Job Search Strategy", icon: "💼" },
    { id: "financial-planning", label: "Financial Planning", icon: "💰" },
    { id: "resume-help", label: "Resume Writing", icon: "📄" },
    { id: "interview-prep", label: "Interview Preparation", icon: "🎯" },
    { id: "education-guidance", label: "Education Planning", icon: "🎓" },
    { id: "family-support", label: "Family Transition Support", icon: "👨‍👩‍👧‍👦" },
    { id: "housing-help", label: "Housing/Relocation", icon: "🏠" },
  ]

  const handleTaskChange = (taskId: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        completedTasks: [...formData.completedTasks, taskId],
      })
    } else {
      setFormData({
        ...formData,
        completedTasks: formData.completedTasks.filter((id: string) => id !== taskId),
      })
    }
  }

  const handleUrgentNeedChange = (needId: string, checked: boolean) => {
    if (checked) {
      setFormData({
        ...formData,
        urgentNeeds: [...formData.urgentNeeds, needId],
      })
    } else {
      setFormData({
        ...formData,
        urgentNeeds: formData.urgentNeeds.filter((id: string) => id !== needId),
      })
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onNext(formData)
  }

  const getCompletionRate = () => {
    return Math.round((formData.completedTasks.length / availableTasks.length) * 100)
  }

  return (
    <div className="space-y-6">
      {/* Purpose Explanation */}
      <Card className="bg-purple-900/20 border-purple-700/30">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <Info className="w-5 h-5 text-purple-400 mt-0.5" />
            <div>
              <h3 className="text-purple-300 font-semibold mb-2">Why We Ask About Your Current Status</h3>
              <p className="text-purple-200 text-sm mb-3">Understanding where you are in your transition helps us:</p>
              <ul className="text-purple-200 text-sm space-y-1">
                <li>• Skip tasks you've already completed</li>
                <li>• Prioritize what you need to do next</li>
                <li>• Set realistic timelines based on your separation date</li>
                <li>• Connect you with urgent resources you need right now</li>
                <li>• Show your progress and keep you motivated</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            Your Current Transition Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="transitionPhase" className="text-slate-300 mb-2 block">
                  What phase are you in? *
                </Label>
                <Select
                  value={formData.transitionPhase}
                  onValueChange={(value) => setFormData({ ...formData, transitionPhase: value })}
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue placeholder="Select phase" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="just-starting">Just starting to think about transition</SelectItem>
                    <SelectItem value="early-planning">Early planning (12+ months out)</SelectItem>
                    <SelectItem value="active-preparation">Active preparation (6-12 months out)</SelectItem>
                    <SelectItem value="final-phase">Final phase (0-6 months out)</SelectItem>
                    <SelectItem value="recently-separated">Recently separated</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="timeUntilSeparation" className="text-slate-300 mb-2 block">
                  Time until separation
                </Label>
                <Select
                  value={formData.timeUntilSeparation}
                  onValueChange={(value) => setFormData({ ...formData, timeUntilSeparation: value })}
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue placeholder="Select timeframe" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="24-months">24+ months</SelectItem>
                    <SelectItem value="12-24-months">12-24 months</SelectItem>
                    <SelectItem value="6-12-months">6-12 months</SelectItem>
                    <SelectItem value="3-6-months">3-6 months</SelectItem>
                    <SelectItem value="0-3-months">0-3 months</SelectItem>
                    <SelectItem value="already-separated">Already separated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label className="text-slate-300 mb-4 block flex items-center">
                <CheckSquare className="w-4 h-4 mr-2" />
                What have you already completed? (Check all that apply)
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {availableTasks.map((task) => (
                  <div key={task.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={task.id}
                      checked={formData.completedTasks.includes(task.id)}
                      onCheckedChange={(checked) => handleTaskChange(task.id, checked as boolean)}
                      className="border-slate-600"
                    />
                    <Label htmlFor={task.id} className="text-slate-300 text-sm cursor-pointer">
                      {task.label}
                    </Label>
                    <Badge variant="outline" className="text-xs border-slate-600 text-slate-400">
                      {task.category}
                    </Badge>
                  </div>
                ))}
              </div>
              <p className="text-slate-400 text-xs mt-2">
                💡 We'll mark these as complete and focus on what you still need to do
              </p>
            </div>

            {/* Progress Preview */}
            {formData.completedTasks.length > 0 && (
              <Card className="bg-green-900/20 border-green-700/30">
                <CardContent className="pt-4">
                  <h4 className="text-green-300 font-medium mb-2">
                    🎉 Great progress! You're {getCompletionRate()}% complete
                  </h4>
                  <p className="text-green-200 text-sm">
                    You've completed {formData.completedTasks.length} of {availableTasks.length} common transition
                    tasks. We'll help you tackle the remaining ones.
                  </p>
                </CardContent>
              </Card>
            )}

            <div>
              <Label className="text-slate-300 mb-4 block flex items-center">
                <AlertTriangle className="w-4 h-4 mr-2" />
                What do you need help with most urgently?
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {urgentNeedOptions.map((need) => (
                  <div key={need.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={need.id}
                      checked={formData.urgentNeeds.includes(need.id)}
                      onCheckedChange={(checked) => handleUrgentNeedChange(need.id, checked as boolean)}
                      className="border-slate-600"
                    />
                    <Label htmlFor={need.id} className="text-slate-300 text-sm cursor-pointer flex items-center">
                      <span className="mr-2">{need.icon}</span>
                      {need.label}
                    </Label>
                  </div>
                ))}
              </div>
              <p className="text-slate-400 text-xs mt-2">
                💡 We'll prioritize these areas and connect you with immediate resources
              </p>
            </div>

            <div className="flex justify-between pt-6">
              {onBack && (
                <Button type="button" variant="outline" onClick={onBack} className="border-slate-600 text-slate-300">
                  Back
                </Button>
              )}
              <Button type="submit" className="bg-blue-600 hover:bg-blue-700 ml-auto">
                Continue
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

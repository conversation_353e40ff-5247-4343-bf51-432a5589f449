"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Shield, Lock, LogIn, Flag, Activity, Eye, EyeOff } from "lucide-react"

interface LoginProps {
  onLogin: (userData: any) => void
  onSignup: () => void
}

export function Login({ onLogin, onSignup }: LoginProps) {
  const [isLogin, setIsLogin] = useState(true)
  const [showPassword, setShowPassword] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
  })

  // Initialize particles effect
  useEffect(() => {
    // Simple star animation effect
    const createStars = () => {
      const starsContainer = document.getElementById('stars-container')
      if (!starsContainer) return

      for (let i = 0; i < 100; i++) {
        const star = document.createElement('div')
        star.className = 'absolute w-1 h-1 bg-white rounded-full opacity-30'
        star.style.left = Math.random() * 100 + '%'
        star.style.top = Math.random() * 100 + '%'
        star.style.animationDelay = Math.random() * 3 + 's'
        star.style.animation = 'twinkle 3s infinite'
        starsContainer.appendChild(star)
      }
    }

    createStars()
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Simulate authentication
    if (isLogin) {
      onLogin({ email: formData.email, isNewUser: false })
    } else {
      onLogin({ email: formData.email, isNewUser: true })
    }
  }

  const handleGoogleAuth = () => {
    // Simulate Google authentication
    onLogin({ email: "<EMAIL>", isNewUser: true, provider: "google" })
  }

  return (
    <>
      {/* Add custom styles */}
      <style jsx>{`
        @keyframes fadeInUp {
          0% { opacity: 0; transform: translateY(20px); }
          100% { opacity: 1; transform: translateY(0); }
        }
        @keyframes twinkle {
          0%, 100% { opacity: 0.3; }
          50% { opacity: 1; }
        }
        .animate-delayed-1 { animation: fadeInUp 0.8s 0.1s both; }
        .animate-delayed-2 { animation: fadeInUp 0.8s 0.25s both; }
        .animate-delayed-3 { animation: fadeInUp 0.8s 0.4s both; }
        .animate-delayed-4 { animation: fadeInUp 0.8s 0.55s both; }
        .animate-delayed-5 { animation: fadeInUp 0.8s 0.7s both; }
        .bg-flag {
          background-image: url('https://images.unsplash.com/photo-1529619752077-8e0e3ff14516?auto=format&fit=crop&w=1200&q=80');
          background-size: cover;
          background-position: center;
        }
        .toggle { width: 2.75rem; height: 1.5rem; }
        .font-montserrat { font-family: 'Montserrat', sans-serif; }
      `}</style>

      <div className="min-h-screen flex flex-col md:flex-row bg-neutral-900 text-neutral-100 relative">
        {/* Stars Particles Background */}
        <div id="stars-container" className="absolute inset-0 -z-10"></div>

        {/* Left Visual Panel */}
        <section className="hidden md:flex w-1/2 bg-flag relative">
          <div className="absolute inset-0 bg-black/60 backdrop-blur-sm"></div>
          <div className="relative z-10 p-10 lg:p-16 flex flex-col justify-end h-full">
            <h1 className="font-montserrat font-semibold text-4xl lg:text-5xl tracking-tight text-white animate-delayed-1">
              Military Transition Command Center
            </h1>
            <p className="mt-4 max-w-md text-neutral-200 animate-delayed-2">
              Empowering service members with seamless tools and resources to navigate civilian life with confidence.
            </p>
            <div className="mt-8 flex space-x-6 animate-delayed-3">
              <Shield className="w-6 h-6 text-blue-400" />
              <Flag className="w-6 h-6 text-red-400" />
              <Activity className="w-6 h-6 text-blue-400" />
            </div>
          </div>
        </section>

        {/* Right Login Panel */}
        <section className="flex-1 flex items-center justify-center p-6 sm:p-12">
          <div className="w-full max-w-md">
            <div className="bg-neutral-800/80 border border-neutral-700 rounded-xl shadow-xl backdrop-blur-lg p-8 space-y-8">

              {/* Logo */}
              <div className="flex items-center space-x-3 animate-delayed-1">
                <Lock className="w-8 h-8 text-blue-400" />
                <span className="font-montserrat font-semibold text-2xl tracking-tight">MTCC Login</span>
              </div>

              {/* Form */}
              <form className="space-y-6" onSubmit={handleSubmit}>

                {/* Email/Username */}
                <div className="animate-delayed-2">
                  <Label htmlFor="email" className="block text-sm font-medium mb-1">
                    {isLogin ? "Username" : "Email Address"}
                  </Label>
                  <Input
                    id="email"
                    type={isLogin ? "text" : "email"}
                    required
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full rounded-lg bg-neutral-700/60 placeholder-neutral-500 border border-neutral-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm px-4 py-3 outline-none"
                    placeholder={isLogin ? "Enter your username" : "Enter your email"}
                  />
                </div>

                {/* Password */}
                <div className="animate-delayed-3">
                  <Label htmlFor="password" className="block text-sm font-medium mb-1">Password</Label>
                  <div className="relative">
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      required
                      value={formData.password}
                      onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                      className="w-full rounded-lg bg-neutral-700/60 placeholder-neutral-500 border border-neutral-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm px-4 py-3 pr-10 outline-none"
                      placeholder="Enter your password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 hover:text-neutral-300"
                    >
                      {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                {/* Confirm Password for Signup */}
                {!isLogin && (
                  <div className="animate-delayed-3">
                    <Label htmlFor="confirmPassword" className="block text-sm font-medium mb-1">Confirm Password</Label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      required
                      value={formData.confirmPassword}
                      onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                      className="w-full rounded-lg bg-neutral-700/60 placeholder-neutral-500 border border-neutral-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm px-4 py-3 outline-none"
                      placeholder="Confirm your password"
                    />
                  </div>
                )}

                {/* Remember + Forgot */}
                {isLogin && (
                  <div className="flex items-center justify-between animate-delayed-4">
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <div className="relative">
                        <input
                          type="checkbox"
                          className="sr-only"
                          checked={rememberMe}
                          onChange={(e) => setRememberMe(e.target.checked)}
                        />
                        <div className={`toggle rounded-full relative transition-colors duration-300 ${
                          rememberMe ? 'bg-blue-500' : 'bg-neutral-600'
                        }`}>
                          <span className={`toggle-dot absolute top-0.5 left-0.5 w-4 h-4 rounded-full bg-white transition-transform duration-300 ${
                            rememberMe ? 'transform translate-x-5' : ''
                          }`}></span>
                        </div>
                      </div>
                      <span className="text-sm">Remember me</span>
                    </label>
                    <button type="button" className="text-sm text-blue-400 hover:underline hover:text-blue-300">
                      Forgot password?
                    </button>
                  </div>
                )}

                {/* Sign-In Button */}
                <Button
                  type="submit"
                  className="w-full inline-flex items-center justify-center gap-2 bg-blue-500 hover:bg-blue-600 text-neutral-900 font-medium rounded-lg px-4 py-3 transition-colors animate-delayed-4"
                >
                  <LogIn className="w-5 h-5" />
                  {isLogin ? "Sign In" : "Create Account"}
                </Button>
              </form>

              {/* Divider */}
              <div className="relative text-center animate-delayed-4">
                <span className="bg-neutral-800/80 px-2 text-sm text-neutral-400">or</span>
                <div className="absolute inset-x-0 top-1/2 -z-10 border-t border-neutral-700"></div>
              </div>

              {/* Social / Guest Options */}
              <div className="space-y-3 animate-delayed-5">
                {/* Google */}
                <Button
                  type="button"
                  onClick={handleGoogleAuth}
                  className="w-full inline-flex items-center justify-center gap-3 bg-white/10 hover:bg-white/20 rounded-lg px-4 py-3 transition-colors focus:ring-2 focus:ring-blue-500 text-white"
                >
                  <svg className="w-5 h-5" viewBox="0 0 488 512" fill="currentColor">
                    <path d="M488 261.8c0-17.1-1.5-34.1-4.6-50.7H249v95.8h134.1c-5.8 31.3-22.6 57.9-48.3 75.8v62.7h78.1c45.8-42.2 72.1-104.3 72.1-183.6z" fill="#4285F4"/>
                    <path d="M249 508c65.5 0 120.4-21.7 160.6-59.1l-78.1-62.7c-21.5 14.4-49.2 22.9-82.6 22.9-63.6 0-117.5-42.8-136.8-100.2h-79v63.1C79.5 455.6 158 508 249 508z" fill="#34A853"/>
                    <path d="M112.2 308.9C106 287.1 106 263.7 112.2 242h-79v-63.1c-16.8 32.9-26.4 70.4-26.4 110.9s9.6 78 26.4 110.9l79-63.1z" fill="#FBBC04"/>
                    <path d="M249 97.1c35.8 0 68.1 12.3 93.4 32.9l70-70C380.3 26.7 317.4 0 249 0 158 0 79.5 52.4 34.1 128.2l79 63.1C131.5 139.9 185.4 97.1 249 97.1z" fill="#EA4335"/>
                  </svg>
                  <span className="font-medium text-sm">Sign in with Google</span>
                </Button>

                {/* Guest */}
                <button
                  type="button"
                  onClick={() => onLogin({ email: "<EMAIL>", isNewUser: false, isGuest: true })}
                  className="w-full inline-flex items-center justify-center gap-2 text-blue-400 hover:text-blue-300 underline text-sm"
                >
                  Continue as Guest
                </button>
              </div>

              {/* Sign-Up */}
              <p className="text-sm text-center animate-delayed-5">
                {isLogin ? "New to MTCC?" : "Already have an account?"}{" "}
                <button
                  type="button"
                  onClick={() => setIsLogin(!isLogin)}
                  className="text-blue-400 hover:underline hover:text-blue-300"
                >
                  {isLogin ? "Request Access" : "Sign In"}
                </button>
              </p>
            </div>
          </div>
        </section>
      </div>
    </>
  )
}

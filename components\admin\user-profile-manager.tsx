"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, Eye, Edit, Trash2, Download, Mail, Phone, MapPin, Shield, Calendar } from "lucide-react"

export function UserProfileManager() {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [selectedUser, setSelectedUser] = useState<any>(null)

  // Mock user data - in production this would come from your backend
  const users = [
    {
      id: 1,
      firstName: "John",
      lastName: "<PERSON>",
      email: "<EMAIL>",
      phone: "(*************",
      rank: "Captain",
      branch: "Navy",
      mos: "Surface Warfare Officer",
      separationDate: "2024-12-15",
      currentLocation: "San Diego, CA",
      targetLocation: "Austin, TX",
      status: "Active",
      onboardingComplete: true,
      lastLogin: "2024-01-15T10:30:00Z",
      progressPercentage: 75,
      topPriorities: ["va-benefits", "job-search", "financial-security"],
      urgentNeeds: ["va-claim-help", "job-search-help"],
    },
    {
      id: 2,
      firstName: "Sarah",
      lastName: "Johnson",
      email: "<EMAIL>",
      phone: "(*************",
      rank: "Master Chief",
      branch: "Navy",
      mos: "Information Systems Technician",
      separationDate: "2024-08-30",
      currentLocation: "Norfolk, VA",
      targetLocation: "Virginia Beach, VA",
      status: "Active",
      onboardingComplete: true,
      lastLogin: "2024-01-15T08:15:00Z",
      progressPercentage: 92,
      topPriorities: ["education", "family-transition", "networking"],
      urgentNeeds: ["education-guidance"],
    },
    {
      id: 3,
      firstName: "Mike",
      lastName: "Rodriguez",
      email: "<EMAIL>",
      phone: "(*************",
      rank: "Staff Sergeant",
      branch: "Marines",
      mos: "Aviation Maintenance",
      separationDate: "2024-06-20",
      currentLocation: "Camp Pendleton, CA",
      targetLocation: "Phoenix, AZ",
      status: "Inactive",
      onboardingComplete: false,
      lastLogin: "2024-01-10T14:22:00Z",
      progressPercentage: 23,
      topPriorities: ["job-search"],
      urgentNeeds: ["resume-help", "interview-prep"],
    },
  ]

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.rank.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.branch.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesFilter =
      filterStatus === "all" ||
      (filterStatus === "active" && user.status === "Active") ||
      (filterStatus === "inactive" && user.status === "Inactive") ||
      (filterStatus === "incomplete" && !user.onboardingComplete)

    return matchesSearch && matchesFilter
  })

  const handleViewUser = (user: any) => {
    setSelectedUser(user)
  }

  const handleEditUser = (user: any) => {
    // In production, this would open an edit modal or navigate to edit page
    console.log("Edit user:", user)
  }

  const handleDeleteUser = (user: any) => {
    // In production, this would show a confirmation dialog
    console.log("Delete user:", user)
  }

  if (selectedUser) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Button onClick={() => setSelectedUser(null)} variant="outline" className="border-slate-600 text-slate-300">
            ← Back to Users
          </Button>
          <div className="flex space-x-2">
            <Button onClick={() => handleEditUser(selectedUser)} className="bg-blue-600 hover:bg-blue-700">
              <Edit className="w-4 h-4 mr-2" />
              Edit Profile
            </Button>
            <Button variant="outline" className="border-slate-600 text-slate-300">
              <Download className="w-4 h-4 mr-2" />
              Export Data
            </Button>
          </div>
        </div>

        {/* User Detail View */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Personal Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-slate-400 text-sm">Full Name</p>
                    <p className="text-white">
                      {selectedUser.firstName} {selectedUser.lastName}
                    </p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Email</p>
                    <p className="text-white flex items-center">
                      <Mail className="w-4 h-4 mr-2" />
                      {selectedUser.email}
                    </p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Phone</p>
                    <p className="text-white flex items-center">
                      <Phone className="w-4 h-4 mr-2" />
                      {selectedUser.phone}
                    </p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Current Location</p>
                    <p className="text-white flex items-center">
                      <MapPin className="w-4 h-4 mr-2" />
                      {selectedUser.currentLocation}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Military Background</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-slate-400 text-sm">Rank</p>
                    <p className="text-white flex items-center">
                      <Shield className="w-4 h-4 mr-2" />
                      {selectedUser.rank}
                    </p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Branch</p>
                    <p className="text-white">{selectedUser.branch}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">MOS/Rating</p>
                    <p className="text-white">{selectedUser.mos}</p>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm">Separation Date</p>
                    <p className="text-white flex items-center">
                      <Calendar className="w-4 h-4 mr-2" />
                      {new Date(selectedUser.separationDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Transition Goals</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-slate-400 text-sm mb-2">Top Priorities</p>
                    <div className="flex flex-wrap gap-2">
                      {selectedUser.topPriorities.map((priority: string, index: number) => (
                        <Badge key={index} variant="outline" className="border-blue-600 text-blue-400">
                          {priority.replace("-", " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <p className="text-slate-400 text-sm mb-2">Urgent Needs</p>
                    <div className="flex flex-wrap gap-2">
                      {selectedUser.urgentNeeds.map((need: string, index: number) => (
                        <Badge key={index} variant="outline" className="border-red-600 text-red-400">
                          {need.replace("-", " ").replace(/\b\w/g, (l: string) => l.toUpperCase())}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Account Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-slate-400">Status</span>
                  <Badge variant={selectedUser.status === "Active" ? "default" : "secondary"}>
                    {selectedUser.status}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Onboarding</span>
                  <Badge variant={selectedUser.onboardingComplete ? "default" : "destructive"}>
                    {selectedUser.onboardingComplete ? "Complete" : "Incomplete"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-400">Progress</span>
                  <span className="text-white">{selectedUser.progressPercentage}%</span>
                </div>
                <div>
                  <span className="text-slate-400 text-sm">Last Login</span>
                  <p className="text-white text-sm">{new Date(selectedUser.lastLogin).toLocaleString()}</p>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Admin Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full bg-blue-600 hover:bg-blue-700">Reset Password</Button>
                <Button variant="outline" className="w-full border-slate-600 text-slate-300">
                  Send Welcome Email
                </Button>
                <Button variant="outline" className="w-full border-slate-600 text-slate-300">
                  View Activity Log
                </Button>
                <Button variant="destructive" className="w-full">
                  Suspend Account
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <Input
                placeholder="Search users by name, email, rank, or branch..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 bg-slate-700 border-slate-600 text-white"
              />
            </div>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-48 bg-slate-700 border-slate-600 text-white">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-slate-700">
                <SelectItem value="all">All Users</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="incomplete">Incomplete Onboarding</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" className="border-slate-600 text-slate-300">
              <Filter className="w-4 h-4 mr-2" />
              Advanced
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">User Profiles ({filteredUsers.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredUsers.map((user) => (
              <div key={user.id} className="border border-slate-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold">
                        {user.firstName[0]}
                        {user.lastName[0]}
                      </span>
                    </div>
                    <div>
                      <h4 className="text-white font-medium">
                        {user.firstName} {user.lastName}
                      </h4>
                      <p className="text-slate-400 text-sm">
                        {user.rank} • {user.branch} • {user.email}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant={user.status === "Active" ? "default" : "secondary"} className="text-xs">
                          {user.status}
                        </Badge>
                        <Badge variant={user.onboardingComplete ? "default" : "destructive"} className="text-xs">
                          {user.onboardingComplete ? "Complete" : "Incomplete"}
                        </Badge>
                        <span className="text-slate-400 text-xs">{user.progressPercentage}% progress</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      onClick={() => handleViewUser(user)}
                      size="sm"
                      variant="outline"
                      className="border-slate-600 text-slate-300"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                    <Button onClick={() => handleEditUser(user)} size="sm" className="bg-blue-600 hover:bg-blue-700">
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button onClick={() => handleDeleteUser(user)} size="sm" variant="destructive">
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

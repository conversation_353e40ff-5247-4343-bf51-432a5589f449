"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { User, MapPin, Phone, Mail } from "lucide-react"

interface BasicInfoProps {
  data: any
  onNext: (data: any) => void
  onBack?: () => void
}

export function BasicInfo({ data, onNext, onBack }: BasicInfoProps) {
  const [formData, setFormData] = useState({
    firstName: data.firstName || "",
    lastName: data.lastName || "",
    email: data.email || "",
    phone: data.phone || "",
    currentLocation: data.currentLocation || "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onNext(formData)
  }

  return (
    <Card className="bg-slate-800 border-slate-700">
      <CardHeader>
        <CardTitle className="text-white flex items-center">
          <User className="w-5 h-5 mr-2" />
          Basic Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName" className="text-slate-300">
                First Name *
              </Label>
              <Input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="John"
                required
              />
            </div>
            <div>
              <Label htmlFor="lastName" className="text-slate-300">
                Last Name *
              </Label>
              <Input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="Smith"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="email" className="text-slate-300">
              Email Address *
            </Label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="pl-10 bg-slate-700 border-slate-600 text-white"
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>

          <div>
            <Label htmlFor="phone" className="text-slate-300">
              Phone Number
            </Label>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="pl-10 bg-slate-700 border-slate-600 text-white"
                placeholder="(*************"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="currentLocation" className="text-slate-300">
              Current Location *
            </Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
              <Input
                id="currentLocation"
                value={formData.currentLocation}
                onChange={(e) => setFormData({ ...formData, currentLocation: e.target.value })}
                className="pl-10 bg-slate-700 border-slate-600 text-white"
                placeholder="San Diego, CA"
                required
              />
            </div>
          </div>

          <div className="flex justify-between pt-6">
            {onBack && (
              <Button type="button" variant="outline" onClick={onBack} className="border-slate-600 text-slate-300">
                Back
              </Button>
            )}
            <Button type="submit" className="bg-blue-600 hover:bg-blue-700 ml-auto">
              Continue
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

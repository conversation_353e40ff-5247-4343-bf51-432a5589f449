"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Target, MapPin, Briefcase, DollarSign, GraduationCap, Info } from "lucide-react"

interface TransitionGoalsProps {
  data: any
  onNext: (data: any) => void
  onBack?: () => void
}

export function TransitionGoals({ data, onNext, onBack }: TransitionGoalsProps) {
  const [formData, setFormData] = useState({
    targetLocation: data.targetLocation || "",
    targetIndustry: data.targetIndustry || "",
    salaryRange: data.salaryRange || "",
    jobType: data.jobType || "",
    educationGoals: data.educationGoals || "",
    careerTimeline: data.careerTimeline || "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onNext(formData)
  }

  const getPersonalizationPreview = () => {
    const previews = []

    if (formData.targetLocation) {
      previews.push(`State benefits for ${formData.targetLocation}`)
    }
    if (formData.targetIndustry) {
      previews.push(`${formData.targetIndustry} job opportunities`)
    }
    if (formData.salaryRange) {
      previews.push(`Financial planning for ${formData.salaryRange} income`)
    }

    return previews
  }

  return (
    <div className="space-y-6">
      {/* Purpose Explanation */}
      <Card className="bg-blue-900/20 border-blue-700/30">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <Info className="w-5 h-5 text-blue-400 mt-0.5" />
            <div>
              <h3 className="text-blue-300 font-semibold mb-2">Why We Ask About Your Goals</h3>
              <p className="text-blue-200 text-sm mb-3">
                Your transition goals help us customize your entire experience. We'll use this information to:
              </p>
              <ul className="text-blue-200 text-sm space-y-1">
                <li>• Show you state-specific benefits for your target location</li>
                <li>• Match you with relevant job opportunities in your preferred industry</li>
                <li>• Calculate cost-of-living differences and financial planning</li>
                <li>• Connect you with mentors in your target field</li>
                <li>• Prioritize education benefits that align with your career path</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-slate-800 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white flex items-center">
            <Target className="w-5 h-5 mr-2" />
            Your Transition Goals
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="targetLocation" className="text-slate-300 flex items-center mb-2">
                <MapPin className="w-4 h-4 mr-2" />
                Where do you want to live after transition? *
              </Label>
              <Input
                id="targetLocation"
                value={formData.targetLocation}
                onChange={(e) => setFormData({ ...formData, targetLocation: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="Austin, TX"
                required
              />
              <p className="text-slate-400 text-xs mt-1">
                💡 We'll show you state benefits, cost of living, and job market data for this location
              </p>
            </div>

            <div>
              <Label htmlFor="targetIndustry" className="text-slate-300 flex items-center mb-2">
                <Briefcase className="w-4 h-4 mr-2" />
                What industry interests you most? *
              </Label>
              <Select
                value={formData.targetIndustry}
                onValueChange={(value) => setFormData({ ...formData, targetIndustry: value })}
              >
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="Select industry" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  <SelectItem value="defense-contracting">Defense Contracting</SelectItem>
                  <SelectItem value="technology">Technology</SelectItem>
                  <SelectItem value="cybersecurity">Cybersecurity</SelectItem>
                  <SelectItem value="logistics">Logistics & Supply Chain</SelectItem>
                  <SelectItem value="healthcare">Healthcare</SelectItem>
                  <SelectItem value="education">Education</SelectItem>
                  <SelectItem value="finance">Finance</SelectItem>
                  <SelectItem value="consulting">Consulting</SelectItem>
                  <SelectItem value="government">Government/Public Service</SelectItem>
                  <SelectItem value="manufacturing">Manufacturing</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-slate-400 text-xs mt-1">
                💡 We'll match you with jobs, mentors, and training opportunities in this field
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="salaryRange" className="text-slate-300 flex items-center mb-2">
                  <DollarSign className="w-4 h-4 mr-2" />
                  Target salary range *
                </Label>
                <Select
                  value={formData.salaryRange}
                  onValueChange={(value) => setFormData({ ...formData, salaryRange: value })}
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue placeholder="Select range" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="50k-70k">$50,000 - $70,000</SelectItem>
                    <SelectItem value="70k-90k">$70,000 - $90,000</SelectItem>
                    <SelectItem value="90k-110k">$90,000 - $110,000</SelectItem>
                    <SelectItem value="110k-130k">$110,000 - $130,000</SelectItem>
                    <SelectItem value="130k-150k">$130,000 - $150,000</SelectItem>
                    <SelectItem value="150k+">$150,000+</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-slate-400 text-xs mt-1">
                  💡 We'll create a financial plan and show realistic job opportunities
                </p>
              </div>

              <div>
                <Label htmlFor="jobType" className="text-slate-300 mb-2 block">
                  Work preference
                </Label>
                <Select
                  value={formData.jobType}
                  onValueChange={(value) => setFormData({ ...formData, jobType: value })}
                >
                  <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                    <SelectValue placeholder="Select preference" />
                  </SelectTrigger>
                  <SelectContent className="bg-slate-800 border-slate-700">
                    <SelectItem value="remote">Remote</SelectItem>
                    <SelectItem value="hybrid">Hybrid</SelectItem>
                    <SelectItem value="on-site">On-site</SelectItem>
                    <SelectItem value="flexible">Flexible</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="educationGoals" className="text-slate-300 flex items-center mb-2">
                <GraduationCap className="w-4 h-4 mr-2" />
                Education or certification goals
              </Label>
              <Textarea
                id="educationGoals"
                value={formData.educationGoals}
                onChange={(e) => setFormData({ ...formData, educationGoals: e.target.value })}
                className="bg-slate-700 border-slate-600 text-white"
                placeholder="e.g., Complete MBA, Get PMP certification, Learn cybersecurity..."
                rows={3}
              />
              <p className="text-slate-400 text-xs mt-1">
                💡 We'll help you use your GI Bill benefits strategically and find relevant programs
              </p>
            </div>

            <div>
              <Label htmlFor="careerTimeline" className="text-slate-300 mb-2 block">
                How quickly do you want to start your new career?
              </Label>
              <Select
                value={formData.careerTimeline}
                onValueChange={(value) => setFormData({ ...formData, careerTimeline: value })}
              >
                <SelectTrigger className="bg-slate-700 border-slate-600 text-white">
                  <SelectValue placeholder="Select timeline" />
                </SelectTrigger>
                <SelectContent className="bg-slate-800 border-slate-700">
                  <SelectItem value="immediately">Immediately after separation</SelectItem>
                  <SelectItem value="1-3-months">1-3 months after</SelectItem>
                  <SelectItem value="3-6-months">3-6 months after</SelectItem>
                  <SelectItem value="6-12-months">6-12 months after</SelectItem>
                  <SelectItem value="take-break">Take a break first</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-slate-400 text-xs mt-1">
                💡 We'll adjust your job search timeline and preparation schedule accordingly
              </p>
            </div>

            {/* Live Preview */}
            {getPersonalizationPreview().length > 0 && (
              <Card className="bg-green-900/20 border-green-700/30">
                <CardContent className="pt-4">
                  <h4 className="text-green-300 font-medium mb-2">🎯 Your Personalized Experience Will Include:</h4>
                  <div className="flex flex-wrap gap-2">
                    {getPersonalizationPreview().map((preview, index) => (
                      <Badge key={index} variant="outline" className="border-green-600 text-green-400">
                        {preview}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            <div className="flex justify-between pt-6">
              {onBack && (
                <Button type="button" variant="outline" onClick={onBack} className="border-slate-600 text-slate-300">
                  Back
                </Button>
              )}
              <Button type="submit" className="bg-blue-600 hover:bg-blue-700 ml-auto">
                Continue
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
